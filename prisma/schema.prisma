generator client {
  provider = "prisma-client-js"
  binaryTargets = ["native", "debian-openssl-3.0.x"]
  engineType = "library"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                     String                  @id @default(cuid())
  email                  String                  @unique
  name                   String?
  password               String?
  role                   UserRole                @default(ORGANIZATION_USER)
  emailVerified          DateTime?
  jobTitle               String?
  departmentName         String?
  phoneNumber            String?
  profileImage           String?
  bio                    String?
  lastLoginAt            DateTime?
  twoFactorEnabled       Boolean                 @default(false)
  preferences            Json?
  createdAt              DateTime                @default(now())
  updatedAt              DateTime                @updatedAt
  organizationId         String?
  departmentId           String?
  divisionId             String?
  amlCheck               AmlCheck?
  apiIntegrationsCreated ApiIntegration[]        @relation("ApiIntegrationCreator")
  auditLogs              AuditLog[]
  baselinesApproved      BaselineConfiguration[] @relation("BaselineApprover")
  baselinesCreated       BaselineConfiguration[] @relation("BaselineCreator")
  carbonCredits          CarbonCredit[]
  complianceChecks       ComplianceCheck[]
  complianceDocuments    ComplianceDocument[]
  complianceReports      ComplianceReport[]
  emissionCalculations   EmissionCalculation[]   @relation("EmissionCalculator")
  emissionVerifications  EmissionCalculation[]   @relation("EmissionVerifier")
  kycVerification        KycVerification?
  MarketplaceListing     MarketplaceListing[]
  MarketplaceWatchlist   MarketplaceWatchlist[]
  notifications          Notification[]
  notificationPreference NotificationPreference?
  onboardingState        OnboardingState?
  buyOrders              Order[]                 @relation("BuyerOrders")
  sellOrders             Order[]                 @relation("SellerOrders")
  organizationDraft      OrganizationDraft?
  resetTokens            PasswordResetToken[]
  permissionGrants       PermissionGrant[]
  permissionApprovals    PermissionRequest[]     @relation("Approver")
  permissionRequests     PermissionRequest[]
  retirements            Retirement[]
  taxReports             TaxReport[]
  teamMemberships        TeamMember[]
  temporaryPermissions   TemporaryPermission[]
  tokenizations          Tokenization[]
  unitLogsLogged         UnitLog[]               @relation("UnitLogLogger")
  unitLogsVerified       UnitLog[]               @relation("UnitLogVerifier")
  unitLogApprovalsGiven  UnitLogCorrection[]     @relation("UnitLogApprover")
  unitLogCorrections     UnitLogCorrection[]     @relation("UnitLogCorrector")
  department             Department?             @relation("UserDepartment", fields: [departmentId], references: [id])
  division               Division?               @relation("UserDivision", fields: [divisionId], references: [id])
  organization           Organization?           @relation(fields: [organizationId], references: [id])
  customRoles            UserCustomRole[]
  verificationTokens     VerificationToken[]
  otpVerifications       OtpVerification[]
  wallets                Wallet[]
  brokerClients          BrokerClient[]
  broker                 Broker?
  spvUser                SPVUser?
  projectAssignments     ProjectAssignment[]     @relation("AssignedByUser")
  dataVerificationLogs   DataVerificationLog[]
  spvDocumentsUploaded   SPVDocument[]           @relation("SPVDocumentUploader")
  spvDocumentsVerified   SPVDocument[]           @relation("SPVDocumentVerifier")
}

model OnboardingState {
  id             String   @id @default(cuid())
  userId         String   @unique
  currentStep    String   @default("organization_details")
  organizationId String?
  skippedSteps   Json     @default("[]")
  completedSteps Json     @default("[]")
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model OrganizationDraft {
  id                 String            @id @default(cuid())
  userId             String            @unique
  name               String?
  description        String?
  website            String?
  industry           String?
  size               OrganizationSize?
  legalName          String?
  registrationNumber String?
  taxId              String?
  country            String?
  address            String?
  city               String?
  state              String?
  postalCode         String?
  phoneNumber        String?
  email              String?
  foundedYear        Int?
  currentStep        Int?              @default(1)
  createdAt          DateTime          @default(now())
  updatedAt          DateTime          @updatedAt
  user               User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model Organization {
  id                    String                    @id @default(cuid())
  name                  String
  description           String?
  website               String?
  logo                  String?
  status                OrganizationStatus        @default(PENDING)
  verificationStatus    VerificationStatus        @default(PENDING)
  legalName             String?
  registrationNumber    String?
  taxId                 String?
  country               String?
  address               String?
  city                  String?
  state                 String?
  postalCode            String?
  phoneNumber           String?
  industry              String?
  size                  OrganizationSize?
  foundedYear           Int?
  primaryContact        String?
  primaryContactEmail   String?
  primaryContactPhone   String?
  createdAt             DateTime                  @default(now())
  updatedAt             DateTime                  @updatedAt
  transactionFeeRate    Float                     @default(0.01)
  listingFeeRate        Float                     @default(0.005)
  subscriptionFee       Float                     @default(0.0)
  amlCheck              AmlCheck?
  auditLogs             AuditLog[]
  billingHistory        BillingRecord[]
  carbonCredits         CarbonCredit[]
  complianceChecks      ComplianceCheck[]
  complianceDocuments   ComplianceDocument[]
  complianceReports     ComplianceReport[]
  customRoles           CustomRole[]
  departments           Department[]
  divisions             Division[]
  documents             Document[]
  financialMetrics      FinancialMetric[]
  financialReports      FinancialReport[]
  invitations           Invitation[]
  kycVerification       KycVerification?
  MarketplaceListing    MarketplaceListing[]
  marketplaceWatchlists MarketplaceWatchlist[]
  notifications         Notification[]
  feeHistory            OrganizationFeeHistory[]
  rbacSettings          OrganizationRbacSettings?
  paymentMethods        PaymentMethod[]
  periodComparisons     PeriodComparison[]
  projects              Project[]
  retirements           Retirement[]
  subscription          Subscription?
  taxReports            TaxReport[]
  teams                 Team[]
  tokenizations         Tokenization[]
  users                 User[]
  wallets               Wallet[]
  brokerClients         BrokerClient[]
  spvs                  SPV[]
}

model SPV {
  id                 String       @id @default(cuid())
  name               String
  purpose            String?
  jurisdiction       String?      // Optional for backward compatibility
  status             SPVStatus    @default(ACTIVE)
  establishedDate    DateTime?
  legalStructure     String?
  registrationNumber String?
  taxId              String?
  address            String?
  description        String?
  // New fields from PRD - made optional for backward compatibility
  country            String?      // Will be required for new SPVs
  legalEntityId      String?      // Will be required for new SPVs - Legal Entity ID
  email              String?      // Will be required for new SPVs - SPV contact email
  contact            String?      // Will be required for new SPVs - Contact person/info
  projectCategories  String[]     @default([]) // Array of project categories
  metadata           Json?

  // SPV Verification Fields from PRD
  gstNumber          String?      // GSTIN (for Indian companies)
  cinNumber          String?      // Corporate Identification Number
  panNumber          String?      // Permanent Account Number
  incorporationDate  DateTime?    // Date of company incorporation
  registeredAddress  String?      // Full registered office address
  contactPersonName  String?      // Name of main POC
  contactPersonEmail String?      // POC email
  contactPersonMobile String?     // POC mobile
  bankAccountNumber  String?      // For settlements
  ifscCode           String?      // Indian Financial System Code
  verificationStatus SPVVerificationStatus @default(PENDING_VERIFICATION)
  verificationNotes  String?      // Admin notes during verification
  verifiedBy         String?      // ID of user who verified
  verifiedAt         DateTime?    // When verification was completed
  rejectionReason    String?      // Reason for rejection if applicable

  createdAt          DateTime     @default(now())
  updatedAt          DateTime     @updatedAt
  organizationId     String
  projects           Project[]
  spvUsers           SPVUser[]
  documents          SPVDocument[]
  organization       Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@map("spvs")
}

model SPVDocument {
  id          String            @id @default(cuid())
  spvId       String
  documentType SPVDocumentType
  fileName    String
  fileUrl     String
  fileSize    Int?
  mimeType    String?
  uploadedBy  String           // User ID who uploaded
  uploadedAt  DateTime         @default(now())
  verified    Boolean          @default(false)
  verifiedBy  String?          // User ID who verified
  verifiedAt  DateTime?
  notes       String?          // Verification notes
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt

  spv         SPV              @relation(fields: [spvId], references: [id], onDelete: Cascade)
  uploader    User             @relation("SPVDocumentUploader", fields: [uploadedBy], references: [id])
  verifier    User?            @relation("SPVDocumentVerifier", fields: [verifiedBy], references: [id])

  @@map("spv_documents")
}

model OrganizationFeeHistory {
  id                 String       @id @default(cuid())
  listingFeeRate     Float
  transactionFeeRate Float
  subscriptionFee    Float
  effectiveFrom      DateTime
  notes              String?
  createdAt          DateTime     @default(now())
  updatedAt          DateTime     @updatedAt
  organizationId     String
  organization       Organization @relation(fields: [organizationId], references: [id])
}

model Project {
  id                   String                   @id @default(cuid())
  name                 String
  description          String?
  type                 ProjectType
  status               ProjectStatus            @default(PENDING)
  verificationStatus   VerificationStatus       @default(PENDING)
  startDate            DateTime?
  endDate              DateTime?
  location             String?
  country              String?
  coordinates          String?
  area                 Float?
  externalProjectId    String?
  registryId           String?
  standard             String?
  methodology          String?
  methodologyVersion   String?
  estimatedReductions  Float?
  actualReductions     Float?
  verifier             String?
  validator            String?
  images               String[]
  budget               Float?
  roi                  Float?
  sdgs                 String[]
  tags                 String[]
  metadata             Json?
  createdAt            DateTime                 @default(now())
  updatedAt            DateTime                 @updatedAt
  organizationId       String
  spvId                String?
  apiIntegrations      ApiIntegration[]
  baselineConfig       BaselineConfiguration?
  carbonCredits        CarbonCredit[]
  complianceDocuments  ComplianceDocument[]
  emissionCalculations EmissionCalculation[]
  organization         Organization             @relation(fields: [organizationId], references: [id])
  spv                  SPV?                     @relation(fields: [spvId], references: [id])
  documents            ProjectDocument[]
  financialMetrics     ProjectFinancialMetric[]
  verificationHistory  ProjectVerification[]
  unitLogs             UnitLog[]
  wallets              Wallet[]
  spvAssignments       ProjectAssignment[]
}

model ProjectDocument {
  id        String              @id @default(cuid())
  name      String
  type      ProjectDocumentType
  url       String
  status    DocumentStatus      @default(PENDING)
  notes     String?
  createdAt DateTime            @default(now())
  updatedAt DateTime            @updatedAt
  projectId String
  project   Project             @relation(fields: [projectId], references: [id], onDelete: Cascade)
}

model ProjectVerification {
  id            String             @id @default(cuid())
  status        VerificationStatus
  verifier      String?
  verifierEmail String?
  notes         String?
  timestamp     DateTime           @default(now())
  metadata      Json?
  projectId     String
  project       Project            @relation(fields: [projectId], references: [id], onDelete: Cascade)
}

model ProjectFinancialMetric {
  id            String              @id @default(cuid())
  metricType    FinancialMetricType
  name          String
  value         Float
  previousValue Float?
  changePercent Float?
  currency      String              @default("INR")
  period        String
  startDate     DateTime
  endDate       DateTime
  target        Float?
  status        MetricStatus?
  notes         String?
  createdAt     DateTime            @default(now())
  updatedAt     DateTime            @updatedAt
  projectId     String
  project       Project             @relation(fields: [projectId], references: [id])
}

model Wallet {
  id                    String                 @id @default(cuid())
  name                  String?
  address               String
  network               String
  chainId               Int
  isTestnet             Boolean                @default(true)
  walletType            WalletType             @default(GENERAL)
  purpose               String?
  encryptedKey          String?
  isSmartWallet         Boolean                @default(false)
  smartAccountAddress   String?
  ownerAddress          String?
  factoryAddress        String?
  implementationAddress String?
  balance               Float                  @default(0)
  lastSyncedAt          DateTime?
  securityScore         Int?
  recoveryEnabled       Boolean                @default(false)
  recoveryType          WalletRecoveryType?
  recoveryData          Json?
  transactionLimitDaily Float?
  transactionLimitPerTx Float?
  requireApprovals      Boolean                @default(false)
  approvalThreshold     Int?
  createdAt             DateTime               @default(now())
  updatedAt             DateTime               @updatedAt
  userId                String?
  organizationId        String?
  projectId             String?
  bridgeTransactions    BridgeTransaction[]
  gasSettings           GasSetting?
  nfts                  NFT[]
  tokens                Token[]
  tokenizations         Tokenization[]
  transactions          Transaction[]
  organization          Organization?          @relation(fields: [organizationId], references: [id])
  project               Project?               @relation(fields: [projectId], references: [id])
  user                  User?                  @relation(fields: [userId], references: [id])
  accessControls        WalletAccessControl[]
  auditLogs             WalletAuditLog[]
  guardians             WalletGuardian[]
  recoveryRequests      WalletRecovery[]
  securitySettings      WalletSecuritySetting?

  @@unique([address, network, chainId])
}

model WalletSecuritySetting {
  id                       String    @id @default(cuid())
  twoFactorEnabled         Boolean   @default(false)
  twoFactorType            String?
  whitelistedAddresses     Json?
  blacklistedAddresses     Json?
  delayedWithdrawals       Boolean   @default(false)
  withdrawalDelayHours     Int?
  notificationsEnabled     Boolean   @default(true)
  autoLockEnabled          Boolean   @default(false)
  autoLockTimeoutMinutes   Int?
  spendingNotifications    Boolean   @default(true)
  unusualActivityDetection Boolean   @default(true)
  lastSecurityReview       DateTime?
  securityReviewFrequency  Int?
  createdAt                DateTime  @default(now())
  updatedAt                DateTime  @updatedAt
  walletId                 String    @unique
  wallet                   Wallet    @relation(fields: [walletId], references: [id], onDelete: Cascade)
}

model WalletAccessControl {
  id           String            @id @default(cuid())
  walletId     String
  userId       String
  accessLevel  WalletAccessLevel
  canApprove   Boolean           @default(false)
  canInitiate  Boolean           @default(false)
  canView      Boolean           @default(true)
  customLimits Json?
  expiresAt    DateTime?
  createdAt    DateTime          @default(now())
  updatedAt    DateTime          @updatedAt
  wallet       Wallet            @relation(fields: [walletId], references: [id], onDelete: Cascade)

  @@unique([walletId, userId])
}

model WalletAuditLog {
  id        String   @id @default(cuid())
  walletId  String
  action    String
  userId    String?
  ipAddress String?
  userAgent String?
  details   Json?
  timestamp DateTime @default(now())
  wallet    Wallet   @relation(fields: [walletId], references: [id], onDelete: Cascade)
}

model BridgeTransaction {
  id                   String       @id @default(cuid())
  sourceWalletId       String
  sourceNetwork        String
  sourceChainId        Int
  destinationAddress   String
  destinationNetwork   String
  destinationChainId   Int
  tokenAddress         String?
  tokenSymbol          String?
  amount               String
  fee                  String?
  status               BridgeStatus @default(PENDING)
  sourceTxHash         String?
  destinationTxHash    String?
  errorMessage         String?
  bridgeProvider       String
  estimatedTimeMinutes Int?
  createdAt            DateTime     @default(now())
  updatedAt            DateTime     @updatedAt
  sourceWallet         Wallet       @relation(fields: [sourceWalletId], references: [id])
}

model GasSetting {
  id                    String   @id @default(cuid())
  walletId              String   @unique
  defaultGasPrice       Float?
  maxGasPrice           Float?
  defaultMaxPriorityFee Float?
  defaultMaxFeePerGas   Float?
  gasLimitMultiplier    Float    @default(1.1)
  optimizationEnabled   Boolean  @default(true)
  alertThreshold        Float?
  alertEnabled          Boolean  @default(false)
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
  wallet                Wallet   @relation(fields: [walletId], references: [id], onDelete: Cascade)
}

model CarbonCredit {
  id                    String                     @id @default(cuid())
  name                  String
  description           String?
  quantity              Float
  availableQuantity     Float
  retiredQuantity       Float                      @default(0)
  price                 Float
  minPurchaseQuantity   Float?
  vintage               Int
  standard              String
  methodology           String
  location              String?
  country               String?
  externalProjectId     String?
  serialNumber          String?
  certificationDate     DateTime?
  expirationDate        DateTime?
  verificationBody      String?
  status                CarbonCreditStatus         @default(PENDING)
  verificationStatus    VerificationStatus         @default(PENDING)
  listingDate           DateTime?
  retirementDate        DateTime?
  retirementReason      String?
  retirementBeneficiary String?
  images                String[]
  tokenId               String?
  contractAddress       String?
  chainId               Int?
  metadata              Json?
  createdAt             DateTime                   @default(now())
  updatedAt             DateTime                   @updatedAt
  userId                String
  organizationId        String
  projectId             String
  valuations            AssetValuation[]
  organization          Organization               @relation(fields: [organizationId], references: [id])
  project               Project                    @relation(fields: [projectId], references: [id])
  user                  User                       @relation(fields: [userId], references: [id])
  documents             CarbonCreditDocument[]
  priceHistory          CarbonCreditPrice[]
  verificationHistory   CarbonCreditVerification[]
  complianceDocuments   ComplianceDocument[]       @relation("CarbonCreditCompliance")
  marketplaceListings   MarketplaceListing[]
  orders                Order[]
  retirements           Retirement[]
  tokenizations         Tokenization[]
}

model Tokenization {
  id                 String             @id @default(cuid())
  tokenId            String
  amount             Float
  network            String
  chainId            Int?
  contractAddress    String?
  transactionHash    String?
  status             TokenizationStatus @default(PENDING)
  tokenStandard      TokenStandard      @default(ERC20)
  tokenName          String?
  tokenSymbol        String?
  tokenDecimals      Int                @default(18)
  tokenizationMethod TokenizationMethod @default(STANDARD)
  metadata           Json?
  createdAt          DateTime           @default(now())
  updatedAt          DateTime           @updatedAt
  carbonCreditId     String
  organizationId     String
  userId             String
  walletId           String?
  carbonCredit       CarbonCredit       @relation(fields: [carbonCreditId], references: [id])
  organization       Organization       @relation(fields: [organizationId], references: [id])
  user               User               @relation(fields: [userId], references: [id])
  wallet             Wallet?            @relation(fields: [walletId], references: [id])
  tokenizationSteps  TokenizationStep[]
}

model TokenizationStep {
  id             String       @id @default(cuid())
  step           String
  status         String       @default("PENDING")
  details        Json?
  errorMessage   String?
  startedAt      DateTime     @default(now())
  completedAt    DateTime?
  tokenizationId String
  tokenization   Tokenization @relation(fields: [tokenizationId], references: [id], onDelete: Cascade)
}

model MarketplaceListing {
  id                  String            @id @default(cuid())
  title               String
  description         String?
  status              MarketplaceStatus @default(PENDING)
  quantity            Float
  availableQuantity   Float
  minPurchaseQuantity Float?
  price               Float?
  pricingStrategy     PricingStrategy   @default(FIXED)
  auctionEndTime      DateTime?
  auctionReservePrice Float?
  auctionMinIncrement Float?
  dynamicPricingRules Json?
  tieredPricingRules  Json?
  visibility          ListingVisibility @default(PUBLIC)
  featured            Boolean           @default(false)
  tags                String[]
  metadata            Json?
  createdAt           DateTime          @default(now())
  updatedAt           DateTime          @updatedAt
  userId              String
  organizationId      String
  carbonCreditId      String
  carbonCredit        CarbonCredit      @relation(fields: [carbonCreditId], references: [id])
  organization        Organization      @relation(fields: [organizationId], references: [id])
  user                User              @relation(fields: [userId], references: [id])
  orders              Order[]
  watchlistItems      WatchlistItem[]
}

model MarketplaceWatchlist {
  id             String          @id @default(cuid())
  name           String
  description    String?
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  userId         String
  organizationId String?
  organization   Organization?   @relation(fields: [organizationId], references: [id])
  user           User            @relation(fields: [userId], references: [id])
  watchlistItems WatchlistItem[]
}

model WatchlistItem {
  id                  String               @id @default(cuid())
  priceAlertEnabled   Boolean              @default(false)
  priceAlertThreshold Float?
  priceAlertDirection PriceAlertDirection?
  notes               String?
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @updatedAt
  watchlistId         String
  listingId           String
  listing             MarketplaceListing   @relation(fields: [listingId], references: [id], onDelete: Cascade)
  watchlist           MarketplaceWatchlist @relation(fields: [watchlistId], references: [id], onDelete: Cascade)
}

model Retirement {
  id              String       @id @default(cuid())
  amount          Float
  reason          String?
  beneficiary     String?
  tokenId         String?
  network         String?
  chainId         Int?
  contractAddress String?
  transactionHash String?
  status          String       @default("COMPLETED")
  createdAt       DateTime     @default(now())
  updatedAt       DateTime     @updatedAt
  carbonCreditId  String
  organizationId  String
  userId          String
  carbonCredit    CarbonCredit @relation(fields: [carbonCreditId], references: [id])
  organization    Organization @relation(fields: [organizationId], references: [id])
  user            User         @relation(fields: [userId], references: [id])
}

model Order {
  id                   String              @id @default(cuid())
  type                 OrderType
  quantity             Float
  price                Float
  status               OrderStatus         @default(PENDING)
  createdAt            DateTime            @default(now())
  updatedAt            DateTime            @updatedAt
  buyerId              String
  sellerId             String
  carbonCreditId       String
  marketplaceListingId String?
  buyer                User                @relation("BuyerOrders", fields: [buyerId], references: [id])
  carbonCredit         CarbonCredit        @relation(fields: [carbonCreditId], references: [id])
  MarketplaceListing   MarketplaceListing? @relation(fields: [marketplaceListingId], references: [id])
  seller               User                @relation("SellerOrders", fields: [sellerId], references: [id])
  transactions         Transaction[]
}

model Transaction {
  id                   String               @id @default(cuid())
  amount               Float
  fee                  Float
  gasPrice             Float?
  gasLimit             Int?
  gasUsed              Int?
  maxFeePerGas         Float?
  maxPriorityFeePerGas Float?
  type                 TransactionType
  status               TransactionStatus    @default(PENDING)
  transactionHash      String?
  blockNumber          Int?
  network              String?
  chainId              Int?
  tokenAddress         String?
  tokenSymbol          String?
  tokenDecimals        Int?
  counterpartyAddress  String?
  counterpartyName     String?
  purpose              String?
  category             TransactionCategory?
  notes                String?
  tags                 String[]
  isRecurring          Boolean              @default(false)
  recurringId          String?
  createdAt            DateTime             @default(now())
  updatedAt            DateTime             @updatedAt
  walletId             String
  orderId              String?
  transactionAuditId   String?
  assetValuationId     String?
  assetValuations      AssetValuation[]     @relation("TransactionValuation")
  order                Order?               @relation(fields: [orderId], references: [id])
  parentAudit          TransactionAudit?    @relation("ParentTransaction", fields: [transactionAuditId], references: [id], map: "Transaction_parentAudit_fkey")
  parentValuation      AssetValuation?      @relation("ParentTransaction", fields: [assetValuationId], references: [id], map: "Transaction_parentValuation_fkey")
  wallet               Wallet               @relation(fields: [walletId], references: [id])
  transactionAudits    TransactionAudit[]   @relation("AuditTransaction")
}

model TransactionAudit {
  id                String        @id @default(cuid())
  transactionId     String
  status            AuditStatus   @default(PENDING)
  verifiedBy        String?
  verifiedAt        DateTime?
  notes             String?
  flagged           Boolean       @default(false)
  flagReason        String?
  flaggedBy         String?
  flaggedAt         DateTime?
  reconciled        Boolean       @default(false)
  reconciledBy      String?
  reconciledAt      DateTime?
  documentUrls      String[]
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt
  childTransactions Transaction[] @relation("ParentTransaction")
  transaction       Transaction   @relation("AuditTransaction", fields: [transactionId], references: [id], onDelete: Cascade)
}

model AssetValuation {
  id                String          @id @default(cuid())
  assetType         AssetType
  assetId           String
  valuationDate     DateTime        @default(now())
  valuationMethod   ValuationMethod
  valueAmount       Float
  valueCurrency     String          @default("USD")
  previousValue     Float?
  changePercentage  Float?
  valuationNotes    String?
  dataSource        String?
  confidence        Int?
  approvedBy        String?
  approvedAt        DateTime?
  transactionId     String?
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  carbonCreditId    String?
  CarbonCredit      CarbonCredit?   @relation(fields: [carbonCreditId], references: [id])
  transaction       Transaction?    @relation("TransactionValuation", fields: [transactionId], references: [id])
  childTransactions Transaction[]   @relation("ParentTransaction")
}

model Subscription {
  id             String             @id @default(cuid())
  plan           SubscriptionPlan
  startDate      DateTime           @default(now())
  endDate        DateTime?
  status         SubscriptionStatus @default(ACTIVE)
  createdAt      DateTime           @default(now())
  updatedAt      DateTime           @updatedAt
  organizationId String             @unique
  organization   Organization       @relation(fields: [organizationId], references: [id])
}

model Notification {
  id             String               @id @default(cuid())
  title          String
  message        String
  read           Boolean              @default(false)
  type           NotificationType
  priority       NotificationPriority @default(NORMAL)
  actionUrl      String?
  actionLabel    String?
  icon           String?
  expiresAt      DateTime?
  metadata       Json?
  createdAt      DateTime             @default(now())
  userId         String
  organizationId String?
  emailSent      Boolean              @default(false)
  emailSentAt    DateTime?
  pushSent       Boolean              @default(false)
  pushSentAt     DateTime?
  organization   Organization?        @relation(fields: [organizationId], references: [id])
  user           User                 @relation(fields: [userId], references: [id])
}

model NotificationPreference {
  id        String   @id @default(cuid())
  email     Boolean  @default(true)
  push      Boolean  @default(true)
  inApp     Boolean  @default(true)
  types     Json?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  userId    String   @unique
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model PasswordResetToken {
  id        String   @id @default(cuid())
  token     String   @unique
  expires   DateTime
  createdAt DateTime @default(now())
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  id        String   @id @default(cuid())
  token     String   @unique
  expires   DateTime
  createdAt DateTime @default(now())
  email     String
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model OtpVerification {
  id        String   @id @default(cuid())
  email     String
  otp       String
  expires   DateTime
  verified  Boolean  @default(false)
  attempts  Int      @default(0)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  userId    String?
  user      User?    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([email])
  @@index([otp])
}

model Token {
  id              String   @id @default(cuid())
  contractAddress String
  name            String?
  symbol          String?
  decimals        Int?
  balance         String
  lastUpdated     DateTime @default(now())
  walletId        String
  wallet          Wallet   @relation(fields: [walletId], references: [id], onDelete: Cascade)

  @@unique([contractAddress, walletId])
}

model NFT {
  id              String   @id @default(cuid())
  contractAddress String
  tokenId         String
  name            String?
  description     String?
  tokenType       String?
  metadata        Json?
  lastUpdated     DateTime @default(now())
  walletId        String
  wallet          Wallet   @relation(fields: [walletId], references: [id], onDelete: Cascade)

  @@unique([contractAddress, tokenId, walletId])
}

model Team {
  id             String          @id @default(cuid())
  name           String
  description    String?
  permissions    Json?
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  organizationId String
  resourceScopes ResourceScope[]
  organization   Organization    @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  members        TeamMember[]
  teamRoles      TeamRole[]
}

model TeamMember {
  id         String       @id @default(cuid())
  role       TeamRoleType @default(MEMBER)
  createdAt  DateTime     @default(now())
  updatedAt  DateTime     @updatedAt
  teamId     String
  userId     String
  teamRoleId String?
  team       Team         @relation(fields: [teamId], references: [id], onDelete: Cascade)
  teamRole   TeamRole?    @relation(fields: [teamRoleId], references: [id])
  user       User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([teamId, userId])
}

model TeamRole {
  id          String       @id @default(cuid())
  name        String
  description String?
  teamId      String
  permissions Json
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  members     TeamMember[]
  team        Team         @relation(fields: [teamId], references: [id], onDelete: Cascade)
}

model ResourceScope {
  id           String      @id @default(cuid())
  teamId       String
  resourceType String
  resourceId   String?
  accessLevel  AccessLevel @default(READ)
  conditions   Json?
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt
  team         Team        @relation(fields: [teamId], references: [id], onDelete: Cascade)

  @@unique([teamId, resourceType, resourceId])
}

model Invitation {
  id             String           @id @default(cuid())
  email          String
  token          String           @unique
  role           UserRole         @default(ORGANIZATION_USER)
  teamId         String?
  expires        DateTime
  status         InvitationStatus @default(PENDING)
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  organizationId String
  organization   Organization     @relation(fields: [organizationId], references: [id], onDelete: Cascade)
}

model Document {
  id             String         @id @default(cuid())
  name           String
  type           DocumentType
  url            String
  status         DocumentStatus @default(PENDING)
  notes          String?
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt
  organizationId String
  organization   Organization   @relation(fields: [organizationId], references: [id], onDelete: Cascade)
}

model PaymentMethod {
  id             String              @id @default(cuid())
  type           PaymentMethodType
  name           String
  status         PaymentMethodStatus @default(ACTIVE)
  isDefault      Boolean             @default(false)
  lastFour       String?
  expiryMonth    Int?
  expiryYear     Int?
  billingAddress String?
  billingCity    String?
  billingState   String?
  billingZip     String?
  billingCountry String?
  metadata       Json?
  createdAt      DateTime            @default(now())
  updatedAt      DateTime            @updatedAt
  organizationId String
  billingRecords BillingRecord[]
  organization   Organization        @relation(fields: [organizationId], references: [id], onDelete: Cascade)
}

model BillingRecord {
  id              String         @id @default(cuid())
  amount          Float
  currency        String         @default("INR")
  description     String
  status          BillingStatus  @default(PENDING)
  dueDate         DateTime?
  paidDate        DateTime?
  invoiceNumber   String?
  invoiceUrl      String?
  receiptUrl      String?
  type            BillingType
  metadata        Json?
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt
  organizationId  String
  paymentMethodId String?
  organization    Organization   @relation(fields: [organizationId], references: [id])
  paymentMethod   PaymentMethod? @relation(fields: [paymentMethodId], references: [id])
}

model CarbonCreditDocument {
  id             String                   @id @default(cuid())
  name           String
  type           CarbonCreditDocumentType
  url            String
  status         DocumentStatus           @default(PENDING)
  notes          String?
  createdAt      DateTime                 @default(now())
  updatedAt      DateTime                 @updatedAt
  carbonCreditId String
  carbonCredit   CarbonCredit             @relation(fields: [carbonCreditId], references: [id], onDelete: Cascade)
}

model CarbonCreditVerification {
  id             String             @id @default(cuid())
  status         VerificationStatus
  verifier       String?
  verifierEmail  String?
  notes          String?
  timestamp      DateTime           @default(now())
  metadata       Json?
  carbonCreditId String
  carbonCredit   CarbonCredit       @relation(fields: [carbonCreditId], references: [id], onDelete: Cascade)
}

model CarbonCreditPrice {
  id             String       @id @default(cuid())
  price          Float
  timestamp      DateTime     @default(now())
  reason         String?
  carbonCreditId String
  carbonCredit   CarbonCredit @relation(fields: [carbonCreditId], references: [id], onDelete: Cascade)
}

model AuditLog {
  id             String        @id @default(cuid())
  type           AuditLogType
  description    String
  metadata       Json?
  ipAddress      String?
  userAgent      String?
  createdAt      DateTime      @default(now())
  userId         String?
  organizationId String?
  organization   Organization? @relation(fields: [organizationId], references: [id])
  user           User?         @relation(fields: [userId], references: [id])
}

model ComplianceReport {
  id             String        @id @default(cuid())
  name           String
  description    String?
  type           String
  format         String        @default("PDF")
  startDate      DateTime
  endDate        DateTime
  filters        Json?
  data           Json?
  url            String?
  generatedBy    String
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  userId         String?
  organizationId String?
  organization   Organization? @relation(fields: [organizationId], references: [id])
  user           User?         @relation(fields: [userId], references: [id])
}

model KycVerification {
  id                  String                   @id @default(cuid())
  status              ComplianceStatus         @default(PENDING)
  level               KycLevel                 @default(NONE)
  lastChecked         DateTime                 @default(now())
  expiresAt           DateTime?
  verifier            String?
  verificationDate    DateTime?
  rejectionReason     String?
  additionalInfo      Json?
  createdAt           DateTime                 @default(now())
  updatedAt           DateTime                 @updatedAt
  userId              String?                  @unique
  organizationId      String?                  @unique
  documents           ComplianceDocument[]
  organization        Organization?            @relation(fields: [organizationId], references: [id])
  user                User?                    @relation(fields: [userId], references: [id])
  verificationHistory KycVerificationHistory[]
}

model KycVerificationHistory {
  id                String           @id @default(cuid())
  status            ComplianceStatus
  notes             String?
  verifier          String?
  timestamp         DateTime         @default(now())
  kycVerificationId String
  kycVerification   KycVerification  @relation(fields: [kycVerificationId], references: [id], onDelete: Cascade)
}

model AmlCheck {
  id             String              @id @default(cuid())
  status         ComplianceStatus    @default(PENDING)
  riskLevel      ComplianceRiskLevel @default(LOW)
  lastChecked    DateTime            @default(now())
  expiresAt      DateTime?
  checkMethod    String?
  checkProvider  String?
  referenceId    String?
  findings       Json?
  actionTaken    String?
  createdAt      DateTime            @default(now())
  updatedAt      DateTime            @updatedAt
  userId         String?             @unique
  organizationId String?             @unique
  amlAlerts      AmlAlert[]
  organization   Organization?       @relation(fields: [organizationId], references: [id])
  user           User?               @relation(fields: [userId], references: [id])
  amlRules       AmlRule[]
}

model AmlAlert {
  id              String              @id @default(cuid())
  title           String
  description     String
  riskLevel       ComplianceRiskLevel
  status          AlertStatus         @default(OPEN)
  source          String
  relatedEntity   String?
  relatedEntityId String?
  assignedTo      String?
  resolvedBy      String?
  resolvedAt      DateTime?
  resolutionNotes String?
  createdAt       DateTime            @default(now())
  updatedAt       DateTime            @updatedAt
  amlCheckId      String
  amlCheck        AmlCheck            @relation(fields: [amlCheckId], references: [id])
}

model AmlRule {
  id          String   @id @default(cuid())
  name        String
  description String?
  ruleType    String
  conditions  Json
  actions     Json
  isActive    Boolean  @default(true)
  priority    Int      @default(0)
  createdBy   String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  amlCheckId  String
  amlCheck    AmlCheck @relation(fields: [amlCheckId], references: [id])
}

model ComplianceDocument {
  id                String                 @id @default(cuid())
  type              ComplianceDocumentType
  name              String
  url               String
  status            ComplianceStatus       @default(PENDING)
  notes             String?
  metadata          Json?
  expiresAt         DateTime?
  verifier          String?
  verificationDate  DateTime?
  rejectionReason   String?
  createdAt         DateTime               @default(now())
  updatedAt         DateTime               @updatedAt
  userId            String?
  organizationId    String?
  kycVerificationId String?
  carbonCreditId    String?
  projectId         String?
  carbonCredit      CarbonCredit?          @relation("CarbonCreditCompliance", fields: [carbonCreditId], references: [id], map: "ComplianceDocument_carbonCreditId_compliance_fkey")
  kycVerification   KycVerification?       @relation(fields: [kycVerificationId], references: [id])
  organization      Organization?          @relation(fields: [organizationId], references: [id])
  project           Project?               @relation(fields: [projectId], references: [id])
  user              User?                  @relation(fields: [userId], references: [id])
}

model ComplianceCheck {
  id              String                @id @default(cuid())
  type            ComplianceCheckType
  result          ComplianceCheckResult
  riskLevel       ComplianceRiskLevel
  details         Json?
  walletAddress   String?
  transactionHash String?
  assetId         String?
  assetType       String?
  checkDate       DateTime              @default(now())
  performedBy     String?
  notes           String?
  createdAt       DateTime              @default(now())
  updatedAt       DateTime              @updatedAt
  userId          String?
  organizationId  String?
  organization    Organization?         @relation(fields: [organizationId], references: [id])
  user            User?                 @relation(fields: [userId], references: [id])
}

model TaxReport {
  id             String        @id @default(cuid())
  year           Int
  quarter        Int?
  format         String        @default("pdf")
  status         String        @default("PENDING")
  url            String?
  data           Json?
  taxableIncome  Float?
  taxableGains   Float?
  deductions     Float?
  taxOwed        Float?
  jurisdiction   String?
  filingDeadline DateTime?
  filedDate      DateTime?
  preparedBy     String?
  reviewedBy     String?
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  userId         String?
  organizationId String?
  organization   Organization? @relation(fields: [organizationId], references: [id])
  user           User?         @relation(fields: [userId], references: [id])
}

model UnitLog {
  id                 String              @id @default(cuid())
  projectId          String
  logDate            DateTime
  frequency          LoggingFrequency    @default(MONTHLY)
  unitType           String
  quantity           Decimal
  dataSource         DataSource          @default(MANUAL)
  sourceFile         String?
  apiSource          String?
  verificationStatus    DataVerificationStatus @default(DRAFT)
  loggedBy              String
  verifiedBy            String?
  verifiedAt            DateTime?
  verificationNotes     String?
  spvApprovedBy         String?
  spvApprovedAt         DateTime?
  spvApprovalNotes      String?
  orgApprovedBy         String?
  orgApprovedAt         DateTime?
  orgApprovalNotes      String?
  notes                 String?
  metadata              Json?
  createdAt             DateTime               @default(now())
  updatedAt             DateTime               @updatedAt
  logger                User                   @relation("UnitLogLogger", fields: [loggedBy], references: [id])
  project               Project                @relation(fields: [projectId], references: [id], onDelete: Cascade)
  verifier              User?                  @relation("UnitLogVerifier", fields: [verifiedBy], references: [id])
  corrections           UnitLogCorrection[]
  verificationLogs      DataVerificationLog[]

  @@index([projectId, logDate])
  @@index([projectId, frequency])
}

model UnitLogCorrection {
  id                String           @id @default(cuid())
  unitLogId         String
  originalQuantity  Decimal
  correctedQuantity Decimal
  reason            String
  correctedBy       String
  approvedBy        String?
  status            CorrectionStatus @default(PENDING)
  notes             String?
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt
  approver          User?            @relation("UnitLogApprover", fields: [approvedBy], references: [id])
  corrector         User             @relation("UnitLogCorrector", fields: [correctedBy], references: [id])
  unitLog           UnitLog          @relation(fields: [unitLogId], references: [id], onDelete: Cascade)
}

model BaselineConfiguration {
  id                 String             @id @default(cuid())
  projectId          String             @unique
  baselineType       BaselineType
  gridEmissionFactor Decimal
  baselineYear       Int
  baselineData       Json
  methodology        String
  validFrom          DateTime
  validTo            DateTime?
  createdBy          String
  approvedBy         String?
  status             VerificationStatus @default(PENDING)
  createdAt          DateTime           @default(now())
  updatedAt          DateTime           @updatedAt
  approver           User?              @relation("BaselineApprover", fields: [approvedBy], references: [id])
  creator            User               @relation("BaselineCreator", fields: [createdBy], references: [id])
  project            Project            @relation(fields: [projectId], references: [id], onDelete: Cascade)
}

model EmissionCalculation {
  id                String             @id @default(cuid())
  projectId         String
  calculationPeriod String
  powerGenerated    Decimal
  emissionReduction Decimal
  baselineEmissions Decimal
  projectEmissions  Decimal
  calculationMethod String
  calculatedBy      String
  verifiedBy        String?
  status            VerificationStatus @default(PENDING)
  metadata          Json?
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt
  calculator        User               @relation("EmissionCalculator", fields: [calculatedBy], references: [id])
  project           Project            @relation(fields: [projectId], references: [id], onDelete: Cascade)
  verifier          User?              @relation("EmissionVerifier", fields: [verifiedBy], references: [id])

  @@index([projectId, calculationPeriod])
}

model ApiIntegration {
  id          String             @id @default(cuid())
  projectId   String
  name        String
  apiType     ApiIntegrationType
  endpoint    String
  authMethod  String
  credentials Json
  dataMapping Json
  frequency   LoggingFrequency
  lastSync    DateTime?
  status      IntegrationStatus  @default(ACTIVE)
  errorCount  Int                @default(0)
  lastError   String?
  createdBy   String
  createdAt   DateTime           @default(now())
  updatedAt   DateTime           @updatedAt
  creator     User               @relation("ApiIntegrationCreator", fields: [createdBy], references: [id])
  project     Project            @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@index([projectId])
}

model FinancialMetric {
  id             String              @id @default(cuid())
  metricType     FinancialMetricType
  name           String
  value          Float
  previousValue  Float?
  changePercent  Float?
  currency       String              @default("INR")
  period         String
  startDate      DateTime
  endDate        DateTime
  target         Float?
  status         MetricStatus?
  notes          String?
  createdAt      DateTime            @default(now())
  updatedAt      DateTime            @updatedAt
  organizationId String
  organization   Organization        @relation(fields: [organizationId], references: [id])
}

model FinancialReport {
  id             String              @id @default(cuid())
  reportType     FinancialReportType
  name           String
  description    String?
  period         String
  startDate      DateTime
  endDate        DateTime
  status         ReportStatus        @default(DRAFT)
  url            String?
  data           Json?
  generatedBy    String?
  approvedBy     String?
  approvedAt     DateTime?
  scheduledId    String?
  createdAt      DateTime            @default(now())
  updatedAt      DateTime            @updatedAt
  organizationId String
  organization   Organization        @relation(fields: [organizationId], references: [id])
}

model PeriodComparison {
  id             String         @id @default(cuid())
  comparisonType ComparisonType
  name           String
  description    String?
  period1Start   DateTime
  period1End     DateTime
  period2Start   DateTime
  period2End     DateTime
  metrics        Json
  changePercent  Float?
  insights       String?
  createdBy      String?
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt
  organizationId String
  organization   Organization   @relation(fields: [organizationId], references: [id])
}

model PlatformSettings {
  id                        String   @id @default(cuid())
  defaultListingFeeRate     Float    @default(2.5)
  defaultTransactionFeeRate Float    @default(1.0)
  defaultSubscriptionFee    Float    @default(0.0)
  minListingFeeRate         Float    @default(0.5)
  maxListingFeeRate         Float    @default(5.0)
  minTransactionFeeRate     Float    @default(0.1)
  maxTransactionFeeRate     Float    @default(3.0)
  createdAt                 DateTime @default(now())
  updatedAt                 DateTime @updatedAt
}

model Permission {
  id                 String                @id @default(cuid())
  name               String                @unique
  displayName        String
  description        String?
  category           String
  createdAt          DateTime              @default(now())
  updatedAt          DateTime              @updatedAt
  permissionGrants   PermissionGrant[]
  permissionRequests PermissionRequest[]
  resourceTypes      ResourcePermission[]
  rolePermissions    RolePermission[]
  tempPermissions    TemporaryPermission[]
}

model CustomRole {
  id             String           @id @default(cuid())
  name           String
  description    String?
  isSystemRole   Boolean          @default(false)
  parentRoleId   String?
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  organizationId String?
  organization   Organization?    @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  parentRole     CustomRole?      @relation("RoleHierarchy", fields: [parentRoleId], references: [id])
  childRoles     CustomRole[]     @relation("RoleHierarchy")
  permissions    RolePermission[]
  userRoles      UserCustomRole[]
}

model RolePermission {
  id           String     @id @default(cuid())
  roleId       String
  permissionId String
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  conditions   Json?
  permission   Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  role         CustomRole @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([roleId, permissionId])
}

model UserCustomRole {
  id        String     @id @default(cuid())
  userId    String
  roleId    String
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt
  expiresAt DateTime?
  grantedBy String?
  scope     Json?
  role      CustomRole @relation(fields: [roleId], references: [id], onDelete: Cascade)
  user      User       @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, roleId])
}

model PermissionGrant {
  id           String     @id @default(cuid())
  userId       String
  permissionId String
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  expiresAt    DateTime?
  grantedBy    String?
  conditions   Json?
  scope        Json?
  permission   Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  user         User       @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, permissionId])
}

model ResourcePermission {
  id           String     @id @default(cuid())
  permissionId String
  resourceType String
  resourceId   String?
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  conditions   Json?
  permission   Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@unique([permissionId, resourceType, resourceId])
}

model TemporaryPermission {
  id           String     @id @default(cuid())
  userId       String
  permissionId String
  createdAt    DateTime   @default(now())
  expiresAt    DateTime
  reason       String
  grantedBy    String
  resourceType String?
  resourceId   String?
  conditions   Json?
  permission   Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  user         User       @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, permissionId, resourceType, resourceId])
}

model PermissionRequest {
  id            String        @id @default(cuid())
  userId        String
  permissionId  String
  status        RequestStatus @default(PENDING)
  reason        String
  resourceType  String?
  resourceId    String?
  duration      Int?
  approverId    String?
  approvalNotes String?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  expiresAt     DateTime?
  approver      User?         @relation("Approver", fields: [approverId], references: [id])
  permission    Permission    @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  user          User          @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model PermissionUsageLog {
  id           String   @id @default(cuid())
  userId       String
  permissionId String
  resourceType String?
  resourceId   String?
  action       String
  success      Boolean
  timestamp    DateTime @default(now())
  ipAddress    String?
  userAgent    String?
  metadata     Json?
}

model Department {
  id             String       @id @default(cuid())
  name           String
  description    String?
  code           String?
  parentId       String?
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  parent         Department?  @relation("DepartmentHierarchy", fields: [parentId], references: [id])
  children       Department[] @relation("DepartmentHierarchy")
  divisions      Division[]
  users          User[]       @relation("UserDepartment")
}

model Division {
  id             String       @id @default(cuid())
  name           String
  description    String?
  code           String?
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  organizationId String
  departmentId   String?
  department     Department?  @relation(fields: [departmentId], references: [id])
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  users          User[]       @relation("UserDivision")
}

model OrganizationRbacSettings {
  id                            String       @id @default(cuid())
  organizationId                String       @unique
  enableCustomRoles             Boolean      @default(false)
  enableResourcePermissions     Boolean      @default(false)
  enableRoleHierarchy           Boolean      @default(false)
  enableTemporaryAccess         Boolean      @default(false)
  enablePermissionRequests      Boolean      @default(false)
  permissionRequestExpiry       Int          @default(72)
  temporaryAccessMaxDuration    Int          @default(168)
  requireApprovalForRoles       Boolean      @default(true)
  requireApprovalForPermissions Boolean      @default(true)
  createdAt                     DateTime     @default(now())
  updatedAt                     DateTime     @updatedAt
  organization                  Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
}

model WalletGuardian {
  id          String                   @id @default(cuid())
  walletId    String
  address     String
  name        String?
  email       String?
  status      String                   @default("ACTIVE")
  addedAt     DateTime                 @default(now())
  updatedAt   DateTime                 @updatedAt
  description String?
  wallet      Wallet                   @relation(fields: [walletId], references: [id], onDelete: Cascade)
  approvals   WalletRecoveryApproval[]

  @@unique([walletId, address])
}

model WalletRecovery {
  id          String                   @id @default(cuid())
  walletId    String
  status      String                   @default("PENDING")
  initiator   String
  timelock    DateTime
  createdAt   DateTime                 @default(now())
  updatedAt   DateTime                 @updatedAt
  executedAt  DateTime?
  newOwner    String?
  description String?
  wallet      Wallet                   @relation(fields: [walletId], references: [id], onDelete: Cascade)
  approvals   WalletRecoveryApproval[]
}

model WalletRecoveryApproval {
  id         String         @id @default(cuid())
  recoveryId String
  guardianId String
  approvedAt DateTime       @default(now())
  signature  String?
  notes      String?
  guardian   WalletGuardian @relation(fields: [guardianId], references: [id])
  recovery   WalletRecovery @relation(fields: [recoveryId], references: [id], onDelete: Cascade)

  @@unique([recoveryId, guardianId])
}

model Broker {
  id                 String                   @id @default(cuid())
  name               String
  legalName          String?
  description        String?
  website            String?
  email              String
  phoneNumber        String?
  address            String?
  city               String?
  state              String?
  postalCode         String?
  country            String?
  licenseNumber      String?
  licenseType        String?
  licenseIssuer      String?
  licenseExpiryDate  DateTime?
  operatingModel     BrokerOperatingModel     @default(FULL_SERVICE)
  commissionRate     Float                    @default(0.025)
  specializations    String[]
  status             BrokerClientStatus       @default(ACTIVE)
  verificationStatus BrokerVerificationStatus @default(PENDING)
  metadata           Json?
  createdAt          DateTime                 @default(now())
  updatedAt          DateTime                 @updatedAt
  userId             String                   @unique
  clients            BrokerClient[]
  commissions        BrokerCommission[]
  documents          BrokerDocument[]
  transactions       BrokerTransaction[]
  user               User                     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("brokers")
}

model BrokerClient {
  id                String              @id @default(cuid())
  brokerId          String
  clientType        BrokerClientType    @default(ORGANIZATION)
  clientName        String
  clientEmail       String
  clientPhone       String?
  relationshipType  String?
  commissionRate    Float?
  contractStartDate DateTime?
  contractEndDate   DateTime?
  notes             String?
  status            BrokerClientStatus  @default(ACTIVE)
  metadata          Json?
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
  organizationId    String?
  userId            String?
  broker            Broker              @relation(fields: [brokerId], references: [id], onDelete: Cascade)
  organization      Organization?       @relation(fields: [organizationId], references: [id])
  user              User?               @relation(fields: [userId], references: [id])
  transactions      BrokerTransaction[]

  @@unique([brokerId, organizationId])
  @@unique([brokerId, userId])
  @@map("broker_clients")
}

model BrokerTransaction {
  id               String                  @id @default(cuid())
  brokerId         String
  transactionType  BrokerTransactionType
  amount           Float
  currency         String                  @default("USD")
  commissionAmount Float
  commissionRate   Float
  description      String?
  referenceId      String?
  status           BrokerTransactionStatus @default(PENDING)
  metadata         Json?
  createdAt        DateTime                @default(now())
  updatedAt        DateTime                @updatedAt
  clientId         String?
  commissions      BrokerCommission[]
  broker           Broker                  @relation(fields: [brokerId], references: [id])
  brokerClient     BrokerClient?           @relation(fields: [clientId], references: [id])

  @@map("broker_transactions")
}

model BrokerCommission {
  id                String                 @id @default(cuid())
  brokerId          String
  amount            Float
  rate              Float
  baseAmount        Float
  currency          String                 @default("USD")
  status            BrokerCommissionStatus @default(PENDING)
  paidDate          DateTime?
  paymentReference  String?
  metadata          Json?
  createdAt         DateTime               @default(now())
  updatedAt         DateTime               @updatedAt
  transactionId     String?
  broker            Broker                 @relation(fields: [brokerId], references: [id])
  brokerTransaction BrokerTransaction?     @relation(fields: [transactionId], references: [id])

  @@map("broker_commissions")
}

model BrokerDocument {
  id               String               @id @default(cuid())
  brokerId         String
  name             String
  type             BrokerDocumentType
  url              String
  description      String?
  status           BrokerDocumentStatus @default(PENDING)
  verifier         String?
  verificationDate DateTime?
  expiryDate       DateTime?
  rejectionReason  String?
  metadata         Json?
  createdAt        DateTime             @default(now())
  updatedAt        DateTime             @updatedAt
  broker           Broker               @relation(fields: [brokerId], references: [id], onDelete: Cascade)

  @@map("broker_documents")
}

enum BrokerClientType {
  ORGANIZATION
  INDIVIDUAL
}

enum BrokerClientStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  TERMINATED
}

enum BrokerTransactionType {
  CARBON_CREDIT_SALE
  CARBON_CREDIT_PURCHASE
  PROJECT_INVESTMENT
  CONSULTATION
  OTHER
}

enum BrokerTransactionStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
}

enum BrokerCommissionStatus {
  PENDING
  CALCULATED
  APPROVED
  PAID
  DISPUTED
}

enum BrokerDocumentType {
  LICENSE
  CERTIFICATION
  KYC_DOCUMENT
  COMPLIANCE_DOCUMENT
  CONTRACT
  OTHER
}

enum BrokerDocumentStatus {
  PENDING
  APPROVED
  REJECTED
  EXPIRED
}

enum BrokerVerificationStatus {
  PENDING
  VERIFIED
  REJECTED
  SUSPENDED
}

enum BrokerOperatingModel {
  FULL_SERVICE
  EXECUTION_ONLY
  ADVISORY_ONLY
  HYBRID
}

enum UserRole {
  ADMIN
  ORGANIZATION_ADMIN
  ORGANIZATION_USER
  BROKER
  INDIVIDUAL
  VERIFIER
  AUDITOR
  MARKETPLACE_ADMIN
  MARKETPLACE_USER
  SYSTEM
  SPV_USER
}

enum SPVStatus {
  ACTIVE
  INACTIVE
  PENDING
  DISSOLVED
}

enum SPVVerificationStatus {
  PENDING_VERIFICATION
  IN_REVIEW
  VERIFIED
  REJECTED
  NEEDS_MORE_INFO
  SUSPENDED
}

enum SPVDocumentType {
  CERTIFICATE_OF_INCORPORATION
  PAN_CARD
  GST_REGISTRATION
  BOARD_RESOLUTION
  BANK_PROOF
  AUTHORIZED_SIGNATORY_ID
  MOA_AOA
  OTHER
}

enum OrganizationStatus {
  PENDING
  ACTIVE
  SUSPENDED
  INACTIVE
}

enum VerificationStatus {
  PENDING
  IN_REVIEW
  VERIFIED
  REJECTED
}

enum SPVUserRole {
  SITE_WORKER
  PROJECT_MANAGER
  SPV_ADMIN
}

enum DataVerificationStatus {
  DRAFT                      // Initial state - data entry
  PM_VERIFIED               // Project Manager verified
  SPV_ADMIN_VERIFIED        // SPV Admin verified
  VERIFIED                  // Final verified state (org admin or auto-verified)
  REJECTED                  // Rejected at any stage
  SPV_APPROVED              // Legacy - keeping for compatibility
  SPV_REJECTED              // Legacy - keeping for compatibility
  ORG_APPROVED              // Organization approved
  ORG_REJECTED              // Organization rejected
  SUBMITTED_TO_VVB          // Submitted to Validation/Verification Body
  VVB_VERIFIED              // VVB verified
  VVB_REJECTED              // VVB rejected
  SUBMITTED_FOR_VERIFICATION // Legacy - keeping for compatibility
}

enum OrganizationSize {
  SMALL
  MEDIUM
  LARGE
  ENTERPRISE
}

enum ProjectType {
  RENEWABLE_ENERGY
  FORESTRY
  METHANE_REDUCTION
  ENERGY_EFFICIENCY
  WASTE_MANAGEMENT
  AGRICULTURE
  TRANSPORTATION
  INDUSTRIAL
  OTHER
}

enum ProjectStatus {
  PENDING
  ACTIVE
  COMPLETED
  SUSPENDED
  CANCELLED
}

enum ProjectDocumentType {
  PROJECT_DESIGN
  METHODOLOGY
  BASELINE_ASSESSMENT
  MONITORING_PLAN
  VALIDATION_REPORT
  VERIFICATION_REPORT
  LEGAL_DOCUMENT
  STAKEHOLDER_CONSULTATION
  ENVIRONMENTAL_IMPACT
  SOCIAL_IMPACT
  FINANCIAL_DOCUMENT
  OTHER
}

enum WalletType {
  GENERAL
  PROJECT
  TOKENIZATION
  TRADING
  RETIREMENT
  CUSTODY
}

enum WalletRecoveryType {
  SOCIAL_RECOVERY
  SEED_PHRASE
  HARDWARE_BACKUP
  MULTI_SIG
  GUARDIAN
}

enum WalletAccessLevel {
  ADMIN
  MANAGER
  APPROVER
  VIEWER
}

enum BridgeStatus {
  PENDING
  INITIATED
  IN_PROGRESS
  COMPLETED
  FAILED
  RECOVERY_NEEDED
}

enum CarbonCreditStatus {
  PENDING
  VERIFIED
  LISTED
  SOLD
  RETIRED
  TOKENIZED
}

enum TokenizationStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  FAILED
  CANCELLED
}

enum TokenStandard {
  ERC20
  ERC1155
  CUSTOM
}

enum TokenizationMethod {
  STANDARD
  BATCH
  CUSTOM
}

enum MarketplaceStatus {
  PENDING
  ACTIVE
  PAUSED
  SOLD
  EXPIRED
  CANCELLED
}

enum PricingStrategy {
  FIXED
  AUCTION
  DYNAMIC
  TIERED
}

enum ListingVisibility {
  PUBLIC
  PRIVATE
  INVITE_ONLY
}

enum PriceAlertDirection {
  ABOVE
  BELOW
  BOTH
}

enum OrderType {
  BUY
  SELL
}

enum OrderStatus {
  PENDING
  MATCHED
  COMPLETED
  CANCELLED
}

enum TransactionType {
  DEPOSIT
  WITHDRAWAL
  PURCHASE
  SALE
  FEE
  BRIDGE
  SWAP
  TRANSFER
  RETIREMENT
  TOKENIZATION
}

enum TransactionStatus {
  PENDING
  COMPLETED
  FAILED
  CANCELLED
  REVERTED
}

enum TransactionCategory {
  INCOME
  EXPENSE
  INVESTMENT
  TRADING
  FEE
  TAX
  TRANSFER
  RETIREMENT
  TOKENIZATION
  OTHER
}

enum AuditStatus {
  PENDING
  VERIFIED
  FLAGGED
  RECONCILED
  NEEDS_REVIEW
}

enum AssetType {
  CARBON_CREDIT
  TOKEN
  NFT
  WALLET
  PORTFOLIO
}

enum ValuationMethod {
  MARKET_PRICE
  HISTORICAL_COST
  FAIR_VALUE
  DISCOUNTED_CASH_FLOW
  COMPARABLE_SALES
  WEIGHTED_AVERAGE
  MANUAL
}

enum SubscriptionPlan {
  FREE
  BASIC
  PREMIUM
  ENTERPRISE
}

enum SubscriptionStatus {
  ACTIVE
  CANCELLED
  EXPIRED
}

enum NotificationType {
  SYSTEM
  ORDER
  TRANSACTION
  CREDIT
  VERIFICATION
  SUBSCRIPTION
  PAYMENT
  BILLING
  TEAM
  SECURITY
  MARKETPLACE
  WALLET
}

enum NotificationPriority {
  LOW
  NORMAL
  HIGH
  URGENT
}

enum TeamRoleType {
  ADMIN
  MANAGER
  MEMBER
  VIEWER
}

enum AccessLevel {
  READ
  WRITE
  ADMIN
  NONE
}

enum InvitationStatus {
  PENDING
  ACCEPTED
  DECLINED
  EXPIRED
}

enum DocumentType {
  BUSINESS_REGISTRATION
  TAX_CERTIFICATE
  IDENTITY_PROOF
  ADDRESS_PROOF
  BANK_STATEMENT
  OTHER
}

enum DocumentStatus {
  PENDING
  APPROVED
  REJECTED
}

enum PaymentMethodType {
  CREDIT_CARD
  BANK_TRANSFER
  CRYPTO_WALLET
  INVOICE
}

enum PaymentMethodStatus {
  ACTIVE
  EXPIRED
  SUSPENDED
}

enum BillingType {
  SUBSCRIPTION
  LISTING_FEE
  TRANSACTION_FEE
  PLATFORM_FEE
  REFUND
}

enum BillingStatus {
  PENDING
  PAID
  FAILED
  REFUNDED
  CANCELLED
}

enum CarbonCreditDocumentType {
  CERTIFICATE
  VERIFICATION_REPORT
  PROJECT_DESCRIPTION
  METHODOLOGY
  MONITORING_REPORT
  VALIDATION_REPORT
  OTHER
}

enum ComplianceStatus {
  PENDING
  IN_REVIEW
  APPROVED
  REJECTED
  EXPIRED
  REQUIRES_UPDATE
}

enum KycLevel {
  NONE
  BASIC
  INTERMEDIATE
  ADVANCED
  ENTERPRISE
}

enum ComplianceRiskLevel {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum AlertStatus {
  OPEN
  IN_PROGRESS
  RESOLVED
  CLOSED
  FALSE_POSITIVE
}

enum ComplianceDocumentType {
  IDENTITY
  ADDRESS_PROOF
  BUSINESS_REGISTRATION
  TAX_CERTIFICATE
  BANK_STATEMENT
  PROJECT_DESCRIPTION
  METHODOLOGY
  VERIFICATION_REPORT
  VALIDATION_REPORT
  REGISTRY_CERTIFICATE
  MONITORING_REPORT
  OTHER
}

enum ComplianceCheckType {
  KYC
  AML
  TRANSACTION_SCREENING
  WALLET_SCREENING
  CARBON_CREDIT_VERIFICATION
  REGULATORY_CHECK
  SANCTIONS_CHECK
  PEP_CHECK
  CUSTOM
}

enum ComplianceCheckResult {
  PASS
  FAIL
  WARNING
  MANUAL_REVIEW
  INCONCLUSIVE
}

enum LoggingFrequency {
  REAL_TIME
  HOURLY
  DAILY
  WEEKLY
  MONTHLY
  QUARTERLY
  YEARLY
}

enum DataSource {
  MANUAL
  CSV_UPLOAD
  API_INTEGRATION
  IOT_DEVICE
}

enum CorrectionStatus {
  PENDING
  APPROVED
  REJECTED
}

enum BaselineType {
  GRID_EMISSION_FACTOR
  HISTORICAL_DATA
  BENCHMARK
  COMBINED_MARGIN
}

enum ApiIntegrationType {
  SMART_METER
  IOT_SENSOR
  WEATHER_API
  GRID_DATA
  EQUIPMENT_MONITORING
  CUSTOM
}

enum IntegrationStatus {
  ACTIVE
  INACTIVE
  ERROR
  MAINTENANCE
}

enum FinancialMetricType {
  TRANSACTION_VOLUME
  REVENUE
  EXPENSE
  WALLET_BALANCE
  CARBON_ASSET_VALUE
  PROFIT_LOSS
  FEE_REVENUE
  TRADING_VOLUME
  AVERAGE_TRANSACTION_SIZE
  RETIREMENT_VOLUME
  TOKENIZATION_VOLUME
}

enum MetricStatus {
  ABOVE_TARGET
  ON_TARGET
  BELOW_TARGET
  CRITICAL
}

enum FinancialReportType {
  TRANSACTION_SUMMARY
  REVENUE_REPORT
  EXPENSE_REPORT
  ASSET_VALUATION
  PROFIT_LOSS
  BALANCE_SHEET
  CASH_FLOW
  TAX_SUMMARY
  TRADING_ACTIVITY
  CUSTOM
}

enum ReportStatus {
  DRAFT
  GENERATED
  APPROVED
  PUBLISHED
  ARCHIVED
}

enum ComparisonType {
  MONTH_OVER_MONTH
  QUARTER_OVER_QUARTER
  YEAR_OVER_YEAR
  CUSTOM
}

enum AuditLogType {
  USER_CREATED
  USER_UPDATED
  USER_DELETED
  ORGANIZATION_CREATED
  ORGANIZATION_UPDATED
  ORGANIZATION_DELETED
  CARBON_CREDIT_CREATED
  CARBON_CREDIT_UPDATED
  CARBON_CREDIT_DELETED
  CARBON_CREDIT_LISTED
  CARBON_CREDIT_UNLISTED
  CARBON_CREDIT_TOKENIZED
  CARBON_CREDIT_RETIRED
  BUY_ORDER_CREATED
  SELL_ORDER_CREATED
  ORDER_MATCHED
  ORDER_CANCELLED
  ORDER_UPDATED
  ORDER_DELETED
  TRANSACTION_CREATED
  TRANSACTION_UPDATED
  TRADE_EXECUTED
  WALLET_CREATED
  WALLET_UPDATED
  WALLET_CONNECTED
  WALLET_DISCONNECTED
  WALLET_SECURITY_UPDATED
  WALLET_ACCESS_GRANTED
  WALLET_ACCESS_REVOKED
  WALLET_RECOVERY_ENABLED
  WALLET_RECOVERY_DISABLED
  WALLET_LIMITS_UPDATED
  WALLET_APPROVAL_REQUIRED
  WALLET_APPROVAL_GRANTED
  WALLET_APPROVAL_REJECTED
  BRIDGE_TRANSACTION_INITIATED
  BRIDGE_TRANSACTION_COMPLETED
  BRIDGE_TRANSACTION_FAILED
  GAS_SETTINGS_UPDATED
  SUBSCRIPTION_CREATED
  SUBSCRIPTION_UPDATED
  LOGIN_SUCCESS
  LOGIN_FAILED
  PASSWORD_RESET
  EMAIL_VERIFIED
  TEAM_CREATED
  TEAM_UPDATED
  TEAM_DELETED
  INVITATION_SENT
  INVITATION_ACCEPTED
  DOCUMENT_UPLOADED
  DOCUMENT_VERIFIED
  PAYMENT_PROCESSED
  BILLING_CREATED
  VERIFICATION_REQUESTED
  VERIFICATION_APPROVED
  VERIFICATION_REJECTED
  TOKENIZATION_REQUESTED
  BATCH_VERIFICATION_REQUESTED
  KYC_VERIFICATION_REQUESTED
  KYC_VERIFICATION_APPROVED
  KYC_VERIFICATION_REJECTED
  AML_CHECK_PERFORMED
  TAX_REPORT_GENERATED
  TRANSACTION_AUDITED
  TRANSACTION_FLAGGED
  TRANSACTION_RECONCILED
  ASSET_VALUATION_CREATED
  ASSET_VALUATION_UPDATED
  ASSET_VALUATION_APPROVED
  FINANCIAL_METRIC_CREATED
  FINANCIAL_METRIC_UPDATED
  FINANCIAL_REPORT_GENERATED
  FINANCIAL_REPORT_APPROVED
  FINANCIAL_REPORT_PUBLISHED
  PERIOD_COMPARISON_CREATED
  PERMISSION_GRANTED
  PERMISSION_REVOKED
  ROLE_CREATED
  ROLE_UPDATED
  ROLE_DELETED
  PERMISSION_REQUESTED
  PERMISSION_REQUEST_APPROVED
  PERMISSION_REQUEST_REJECTED
  TEMPORARY_PERMISSION_GRANTED
  TEMPORARY_PERMISSION_EXPIRED
  ORGANIZATION_FEES_UPDATED
  SETTINGS_UPDATED
  PROJECT_CREATED
  PROJECT_UPDATED
  PROJECT_DELETED
  PROJECT_STATUS_CHANGED
  PROJECT_APPROVED
  PROJECT_REJECTED
  PROJECT_SUSPENDED
  PROJECT_REACTIVATED
  DATA_ENTRY_MANUAL
  DATA_ENTRY_CSV
  DATA_ENTRY_API
  DATA_ENTRY_IOT
  DATA_EDITED
  DATA_DELETED
  DATA_APPROVED
  DATA_REJECTED
  DATA_VERIFIED
  DATA_CORRECTION_REQUESTED
  DATA_CORRECTION_APPROVED
  DATA_CORRECTION_REJECTED
  MONITORING_DATA_CREATED
  MONITORING_DATA_UPDATED
  MONITORING_DATA_DELETED
  MONITORING_DATA_VERIFIED
  MONITORING_DATA_REJECTED
  BASELINE_CONFIG_CREATED
  BASELINE_CONFIG_UPDATED
  EMISSION_CALCULATION_CREATED
  EMISSION_CALCULATION_UPDATED
  API_INTEGRATION_CREATED
  API_INTEGRATION_UPDATED
  API_INTEGRATION_DELETED
  API_SYNC_COMPLETED
  API_SYNC_FAILED
}

enum RequestStatus {
  PENDING
  APPROVED
  REJECTED
  CANCELLED
  EXPIRED
}

model SPVUser {
  id                 String                @id @default(cuid())
  userId             String                @unique
  spvId              String
  role               SPVUserRole
  permissions        Json?                 // Additional custom permissions
  isActive           Boolean               @default(true)
  createdAt          DateTime              @default(now())
  updatedAt          DateTime              @updatedAt

  user               User                  @relation(fields: [userId], references: [id], onDelete: Cascade)
  spv                SPV                   @relation(fields: [spvId], references: [id], onDelete: Cascade)
  projectAssignments ProjectAssignment[]

  @@map("spv_users")
}

model ProjectAssignment {
  id          String    @id @default(cuid())
  projectId   String
  spvUserId   String
  assignedBy  String    // User ID who assigned
  assignedAt  DateTime  @default(now())
  isActive    Boolean   @default(true)
  permissions Json?     // Project-specific permissions

  project     Project   @relation(fields: [projectId], references: [id], onDelete: Cascade)
  spvUser     SPVUser   @relation(fields: [spvUserId], references: [id], onDelete: Cascade)
  assignedByUser User   @relation("AssignedByUser", fields: [assignedBy], references: [id])

  @@unique([projectId, spvUserId])
  @@map("project_assignments")
}

model DataVerificationLog {
  id                String                 @id @default(cuid())
  unitLogId         String
  fromStatus        DataVerificationStatus
  toStatus          DataVerificationStatus
  verifiedBy        String
  verificationNotes String?
  metadata          Json?
  createdAt         DateTime               @default(now())

  unitLog           UnitLog                @relation(fields: [unitLogId], references: [id], onDelete: Cascade)
  verifier          User                   @relation(fields: [verifiedBy], references: [id])

  @@map("data_verification_logs")
}
