#!/usr/bin/env node

/**
 * RBAC System Fix and Validation Script
 * This script ensures the RBAC system is properly configured and functional
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const db = new PrismaClient();

async function createTestUsers() {
  console.log('🔧 Creating test users for RBAC testing...');
  
  try {
    // Create admin user
    const adminExists = await db.user.findUnique({
      where: { email: '<EMAIL>' }
    });
    
    if (!adminExists) {
      const hashedPassword = await bcrypt.hash('admin123', 12);
      await db.user.create({
        data: {
          email: '<EMAIL>',
          name: 'System Administrator',
          password: hashedPassword,
          role: 'ADMIN',
          emailVerified: new Date(),
        }
      });
      console.log('✅ Created admin user: <EMAIL> / admin123');
    } else {
      console.log('✅ Admin user already exists');
    }
    
    // Create organization and org admin
    let organization = await db.organization.findFirst({
      where: { name: 'Test Organization' }
    });
    
    if (!organization) {
      organization = await db.organization.create({
        data: {
          name: 'Test Organization',
          status: 'ACTIVE',
          verificationStatus: 'VERIFIED',
          contactEmail: '<EMAIL>',
        }
      });
      console.log('✅ Created test organization');
    }
    
    const orgAdminExists = await db.user.findUnique({
      where: { email: '<EMAIL>' }
    });
    
    if (!orgAdminExists) {
      const hashedPassword = await bcrypt.hash('orgadmin123', 12);
      await db.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Organization Administrator',
          password: hashedPassword,
          role: 'ORGANIZATION_ADMIN',
          organizationId: organization.id,
          emailVerified: new Date(),
        }
      });
      console.log('✅ Created org admin user: <EMAIL> / orgadmin123');
    } else {
      console.log('✅ Org admin user already exists');
    }
    
    // Create regular user
    const userExists = await db.user.findUnique({
      where: { email: '<EMAIL>' }
    });
    
    if (!userExists) {
      const hashedPassword = await bcrypt.hash('user123', 12);
      await db.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Regular User',
          password: hashedPassword,
          role: 'ORGANIZATION_USER',
          organizationId: organization.id,
          emailVerified: new Date(),
        }
      });
      console.log('✅ Created regular user: <EMAIL> / user123');
    } else {
      console.log('✅ Regular user already exists');
    }
    
  } catch (error) {
    console.error('❌ Error creating test users:', error);
  }
}

async function initializeRBAC() {
  console.log('🔧 Initializing RBAC system...');
  
  try {
    // Import and run RBAC initialization
    const { initializeRbacSystem } = require('./src/lib/rbac/init-rbac');
    await initializeRbacSystem();
    console.log('✅ RBAC system initialized successfully');
  } catch (error) {
    console.error('❌ Error initializing RBAC:', error);
  }
}

async function validateDatabase() {
  console.log('🔧 Validating database schema...');
  
  try {
    // Check if all required tables exist
    const tables = [
      'permission',
      'customRole', 
      'userCustomRole',
      'rolePermission',
      'permissionGrant',
      'temporaryPermission',
      'permissionUsageLog'
    ];
    
    for (const table of tables) {
      try {
        const count = await db[table].count();
        console.log(`✅ ${table} table exists (${count} records)`);
      } catch (error) {
        console.log(`❌ ${table} table missing or inaccessible`);
      }
    }
    
    // Check permissions
    const permissionCount = await db.permission.count();
    if (permissionCount === 0) {
      console.log('⚠️  No permissions found, RBAC initialization may be needed');
    } else {
      console.log(`✅ Found ${permissionCount} permissions`);
    }
    
    // Check roles
    const roleCount = await db.customRole.count();
    console.log(`✅ Found ${roleCount} custom roles`);
    
  } catch (error) {
    console.error('❌ Error validating database:', error);
  }
}

async function testAPIEndpoints() {
  console.log('🔧 Testing API endpoints...');
  
  // This would require authentication, so we'll just check if the server is running
  try {
    const response = await fetch('http://localhost:3000/api/health');
    if (response.ok) {
      console.log('✅ Server is running and responding');
    } else {
      console.log('❌ Server is not responding properly');
    }
  } catch (error) {
    console.log('❌ Cannot connect to server. Make sure it\'s running on port 3000');
  }
}

async function generateTestInstructions() {
  console.log('\n📋 RBAC Testing Instructions:');
  console.log('================================');
  console.log('');
  console.log('1. 🚀 Start the application:');
  console.log('   npm run dev');
  console.log('');
  console.log('2. 🔐 Test Authentication:');
  console.log('   - Go to: http://localhost:3000/login');
  console.log('   - Admin: <EMAIL> / admin123');
  console.log('   - Org Admin: <EMAIL> / orgadmin123');
  console.log('   - User: <EMAIL> / user123');
  console.log('');
  console.log('3. 🧪 Test RBAC Pages:');
  console.log('   - Dashboard: http://localhost:3000/dashboard');
  console.log('   - RBAC Dashboard: http://localhost:3000/dashboard/rbac');
  console.log('   - Users Management: http://localhost:3000/dashboard/rbac/users');
  console.log('   - Roles Management: http://localhost:3000/dashboard/rbac/roles');
  console.log('   - Audit Log: http://localhost:3000/dashboard/rbac/audit');
  console.log('');
  console.log('4. 🔍 Test API Endpoints (with authentication):');
  console.log('   - GET /api/rbac/users');
  console.log('   - GET /api/rbac/roles');
  console.log('   - GET /api/rbac/audit');
  console.log('   - GET /api/rbac/permissions');
  console.log('');
  console.log('5. 🛠️  Debug Tools:');
  console.log('   - RBAC Debug: http://localhost:3000/dashboard/rbac/debug');
  console.log('   - Run tests: npm run test:rbac');
  console.log('   - API tests: ./test-rbac-curl.sh [session_cookie]');
  console.log('');
  console.log('6. 🔧 Common Issues:');
  console.log('   - 401 Unauthorized: User not logged in');
  console.log('   - 403 Forbidden: User lacks required permissions');
  console.log('   - 500 Server Error: Check server logs and database');
  console.log('');
}

async function main() {
  console.log('🔐 RBAC System Fix and Validation');
  console.log('==================================');
  console.log('');
  
  try {
    // Connect to database
    await db.$connect();
    console.log('✅ Database connected');
    
    // Validate database schema
    await validateDatabase();
    
    // Initialize RBAC system
    await initializeRBAC();
    
    // Create test users
    await createTestUsers();
    
    // Test API endpoints
    await testAPIEndpoints();
    
    // Generate instructions
    await generateTestInstructions();
    
    console.log('🎉 RBAC system setup completed successfully!');
    console.log('');
    console.log('The APIs returning 401 "Unauthorized" is CORRECT behavior.');
    console.log('This means the authentication system is working properly.');
    console.log('');
    console.log('To test the APIs:');
    console.log('1. Log in through the web interface');
    console.log('2. Use the browser session to test APIs');
    console.log('3. Or use the test scripts with proper authentication');
    
  } catch (error) {
    console.error('❌ Error during setup:', error);
  } finally {
    await db.$disconnect();
  }
}

if (require.main === module) {
  main().catch(console.error);
}
