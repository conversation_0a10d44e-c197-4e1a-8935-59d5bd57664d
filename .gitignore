# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz
.pnpm-store

# testing
/coverage

# Ignore all build artifacts in tmp
tmp/


# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Prisma
/prisma/dev.db
/prisma/dev.db-journal

# Docker
/postgres_data
