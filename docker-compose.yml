services:
  app-dev:
    build:
      context: .
      target: deps
    ports:
      - "3000:3000"
    depends_on:
      db:
        condition: service_healthy
    env_file:
      - .env
    environment:
      # Override specific Docker-related environment variables
      - DATABASE_URL=postgresql://${DATABASE_USER:-postgres}:${DATABASE_PASSWORD:-root}@db:5432/${DATABASE_NAME:-carbon_exchange}
      - DATABASE_HOST=db
      - DATABASE_PORT=5432
      - NODE_ENV=development
      - NEXTAUTH_URL=http://localhost:3000
      - NEXT_PUBLIC_API_URL=http://localhost:3000
      - EMAIL_SERVER=${SMTP_HOST:-smtp.example.com}
      - EMAIL_FROM=${SMTP_FROM:-<EMAIL>}
      - SMTP_HOST=${SMTP_HOST:-smtp.example.com}
      - SMTP_PORT=${SMTP_PORT:-587}
      - SMTP_USER=${SMTP_USER:-<EMAIL>}
      - SMTP_PASSWORD=${SMTP_PASSWORD:-your-smtp-password}
      - SMTP_FROM=${SMTP_FROM:-<EMAIL>}
      - ALCHEMY_API_KEY=your-alchemy-api-key
      - ALCHEMY_NETWORK=eth-sepolia
      - ETHEREUM_NETWORK=sepolia
      - POLYGON_NETWORK=mumbai
      - OPTIMISM_NETWORK=optimism-sepolia
      - ARBITRUM_NETWORK=arbitrum-sepolia
      - BASE_NETWORK=base-sepolia
      - NODE_ENV=${NODE_ENV:-development}
      - WALLET_ENCRYPTION_KEY=dev-wallet-encryption-key
      - ALCHEMY_GAS_MANAGER_POLICY_ID=dev-gas-manager-policy-id
      - ANALYTICS_ID=dev-analytics-id
      - STORAGE_PROVIDER=local
    volumes:
      - ./:/app
      - /app/.next
    command: /app/scripts/start-dev.sh

  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    depends_on:
      db:
        condition: service_healthy
    env_file:
      - .env
    environment:
      # Override specific Docker-related environment variables
      - DATABASE_URL=postgresql://${DATABASE_USER:-postgres}:${DATABASE_PASSWORD:-root}@db:5432/${DATABASE_NAME:-carbon_exchange}
      - DATABASE_HOST=db
      - DATABASE_PORT=5432
      - NODE_ENV=production
      - NEXTAUTH_URL=http://localhost:3000
      - NEXT_PUBLIC_API_URL=http://localhost:3000
      - EMAIL_SERVER=${SMTP_HOST:-smtp.example.com}
      - EMAIL_FROM=${SMTP_FROM:-<EMAIL>}
      - SMTP_HOST=${SMTP_HOST:-smtp.example.com}
      - SMTP_PORT=${SMTP_PORT:-587}
      - SMTP_USER=${SMTP_USER:-<EMAIL>}
      - SMTP_PASSWORD=${SMTP_PASSWORD:-your-smtp-password}
      - SMTP_FROM=${SMTP_FROM:-<EMAIL>}
      - ALCHEMY_API_KEY=${ALCHEMY_API_KEY:-your-alchemy-api-key}
      - ALCHEMY_NETWORK=${ALCHEMY_NETWORK:-eth-sepolia}
      - ETHEREUM_NETWORK=${ETHEREUM_NETWORK:-mainnet}
      - POLYGON_NETWORK=${POLYGON_NETWORK:-polygon}
      - OPTIMISM_NETWORK=${OPTIMISM_NETWORK:-optimism}
      - ARBITRUM_NETWORK=${ARBITRUM_NETWORK:-arbitrum}
      - BASE_NETWORK=${BASE_NETWORK:-base}
      - NODE_ENV=${NODE_ENV:-production}
      - WALLET_ENCRYPTION_KEY=${WALLET_ENCRYPTION_KEY:-prod-wallet-encryption-key}
      - ALCHEMY_GAS_MANAGER_POLICY_ID=${ALCHEMY_GAS_MANAGER_POLICY_ID:-prod-gas-manager-policy-id}
      - ANALYTICS_ID=${ANALYTICS_ID:-prod-analytics-id}
      - STORAGE_PROVIDER=${STORAGE_PROVIDER:-local}
      - S3_BUCKET=${S3_BUCKET:-}
      - S3_REGION=${S3_REGION:-}
      - S3_ACCESS_KEY=${S3_ACCESS_KEY:-}
      - S3_SECRET_KEY=${S3_SECRET_KEY:-}
      - PRISMA_CLI_BINARY_TARGETS=debian-openssl-3.0.x
      - PRISMA_ENGINES_CHECKSUM_IGNORE_MISSING=1
    volumes:
      - uploads_data:/app/public/uploads
    command: /app/scripts/start.sh

  db-init:
    build:
      context: .
      target: deps
    depends_on:
      db:
        condition: service_healthy
    env_file:
      - .env
    environment:
      # Override specific Docker-related environment variables
      - DATABASE_URL=postgresql://${DATABASE_USER:-postgres}:${DATABASE_PASSWORD:-root}@db:5432/${DATABASE_NAME:-carbon_exchange}
      - DATABASE_HOST=db
      - DATABASE_PORT=5432
      - DATABASE_USER=${DATABASE_USER:-postgres}
      - DATABASE_PASSWORD=${DATABASE_PASSWORD:-root}
      - NODE_ENV=${NODE_ENV:-development}
    volumes:
      - ./:/app
      - /app/node_modules
    command: sh -c "chmod +x /app/scripts/docker-init-db.sh && /app/scripts/docker-init-db.sh"



  db:
    image: postgres:16
    ports:
      - "5434:5432"
    # ports:
    #   - "${DATABASE_PORT:-5433}:5432"
    environment:
      - POSTGRES_USER=${DATABASE_USER:-postgres}
      - POSTGRES_PASSWORD=${DATABASE_PASSWORD:-root}
      - POSTGRES_DB=${DATABASE_NAME:-carbon_exchange}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:
  uploads_data:

networks:
  app_network:
    driver: bridge
