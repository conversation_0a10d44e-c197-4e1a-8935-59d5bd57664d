FROM node:22-alpine AS base

# Install dependencies needed for all stages
RUN apk add --no-cache libc6-compat postgresql-client

# Enable corepack and install pnpm
RUN corepack enable && corepack prepare pnpm@latest --activate

# Set pnpm store directory for better caching
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

# Install dependencies only when needed
FROM base AS deps
WORKDIR /app

# Copy package files for dependency installation
COPY package.json pnpm-lock.yaml* ./

# Copy only essential scripts needed for build
COPY scripts/ensure-uploads-dir.js scripts/node-protocol-transpiler.js ./scripts/

# Install dependencies with aggressive optimizations
RUN --mount=type=cache,id=pnpm,target=/pnpm/store \
    --mount=type=cache,id=pnpm-metadata,target=/app/.pnpm \
    pnpm config set store-dir /pnpm/store && \
    pnpm install --frozen-lockfile --prefer-offline --production=false --ignore-scripts --no-optional

# Build stage
FROM base AS builder
WORKDIR /app

# Copy package.json and pnpm-lock.yaml first for better caching
COPY package.json pnpm-lock.yaml* ./

# Copy node_modules from deps stage (use bind mount for faster copying)
COPY --from=deps /app/node_modules ./node_modules

# Copy only necessary source files for build
COPY prisma ./prisma
COPY src ./src
COPY public ./public
COPY next.config.ts dev-optimized.config.js tsconfig.json postcss.config.mjs ./
COPY components.json ./
COPY scripts ./scripts

# Set build environment variables
ENV NEXT_TELEMETRY_DISABLED=1
ENV NODE_ENV=production

# Run pre-build scripts
RUN node scripts/ensure-uploads-dir.js
RUN node scripts/node-protocol-transpiler.js

# Set Prisma environment variables for production builds
ENV PRISMA_CLI_BINARY_TARGETS="debian-openssl-3.0.x"
ENV PRISMA_ENGINES_CHECKSUM_IGNORE_MISSING=1

# Generate Prisma client with specific binary targets
RUN pnpm prisma generate

# Install any remaining dependencies (in case some were skipped)
RUN pnpm install --production=false --prefer-offline

# Build the application with optimizations and caching
RUN --mount=type=cache,id=nextjs,target=/app/.next/cache \
    --mount=type=cache,id=turbopack,target=/app/.next/cache/turbopack \
    pnpm run build

# Verify standalone output exists and show build structure
RUN echo "Build completed. Checking output structure:" && \
    ls -la . && \
    echo "Contents of .next directory:" && \
    ls -la .next/ && \
    echo "Checking for standalone directory:" && \
    ls -la .next/standalone/ || echo "Standalone directory not found"

# Create uploads directory
RUN mkdir -p public/uploads

# Production runtime stage
FROM node:22-alpine AS runner
WORKDIR /app

# Install only runtime dependencies
RUN apk add --no-cache \
    libc6-compat \
    postgresql-client \
    && rm -rf /var/cache/apk/*

# Enable corepack for pnpm (needed for Prisma commands)
RUN corepack enable && corepack prepare pnpm@latest --activate

# Create non-root user
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 nextjs

# Set production environment
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3000

# Copy built application from builder stage
# Check if .next exists, otherwise use /tmp/.next-carbonx
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static
COPY --from=builder --chown=nextjs:nodejs /app/public ./public

# Copy Prisma files for migrations
COPY --from=builder --chown=nextjs:nodejs /app/prisma ./prisma

# Copy necessary Prisma files for runtime
# Note: Prisma client is bundled in standalone build, but we need CLI for migrations
RUN mkdir -p node_modules/.bin

# Set Prisma environment variables for runtime
ENV PRISMA_CLI_BINARY_TARGETS="debian-openssl-3.0.x"
ENV PRISMA_ENGINES_CHECKSUM_IGNORE_MISSING=1

# Copy essential runtime scripts only
COPY --from=builder --chown=nextjs:nodejs /app/scripts/start.sh ./scripts/
COPY --from=builder --chown=nextjs:nodejs /app/scripts/check-env.js ./scripts/
COPY --from=builder --chown=nextjs:nodejs /app/scripts/docker-init-db.sh ./scripts/
COPY --from=builder --chown=nextjs:nodejs /app/scripts/deployment-seed.js ./scripts/
COPY --from=builder --chown=nextjs:nodejs /app/scripts/deployment-seed.ts ./scripts/

# Copy package.json for pnpm commands
COPY --from=builder --chown=nextjs:nodejs /app/package.json ./

# Set executable permissions
RUN chmod +x scripts/*.sh scripts/*.js

# Create necessary directories with correct permissions
RUN mkdir -p .next node_modules/.bin && chown -R nextjs:nodejs .next node_modules

# Switch to non-root user
USER nextjs

EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:3000/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })" || exit 1

# Use startup script
CMD ["/app/scripts/start.sh"]
