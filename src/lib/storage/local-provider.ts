/**
 * Local File System Storage Provider
 *
 * This provider stores files on the local file system.
 */

import fs from 'fs/promises';
import path from 'path';
import { StorageProvider, UploadResult, DeleteResult, FileMetadata, StorageConfig } from './types';
import { logger } from '@/lib/logger';

export class LocalStorageProvider implements StorageProvider {
  private baseUrl: string;
  private rootDir: string;
  private useRelativeUrls: boolean;

  /**
   * Create a new LocalStorageProvider
   * @param config Storage configuration
   */
  constructor(config: StorageConfig) {
    this.baseUrl = config.baseUrl;
    this.rootDir = config.rootDir || path.join(process.cwd(), 'public', 'uploads');
    this.useRelativeUrls = config.useRelativeUrls || false;
  }

  /**
   * Ensure directory exists
   * @param dirPath Directory path
   */
  private async ensureDir(dirPath: string): Promise<void> {
    try {
      await fs.access(dirPath);
    } catch (error) {
      // Directory doesn't exist, create it
      await fs.mkdir(dirPath, { recursive: true });
    }
  }

  /**
   * Upload a file to local storage
   * @param fileBuffer File buffer
   * @param fileName File name (with path)
   * @param contentType Content type (MIME type)
   * @param metadata Additional metadata
   * @returns Upload result
   */
  async uploadFile(
    fileBuffer: Buffer,
    fileName: string,
    contentType: string,
    metadata?: Record<string, any>
  ): Promise<UploadResult> {
    try {
      // Normalize file name to prevent directory traversal
      const normalizedFileName = fileName.replace(/\.\./g, '').replace(/^\//, '');

      // Get directory path
      const dirPath = path.dirname(path.join(this.rootDir, normalizedFileName));

      // Ensure directory exists
      await this.ensureDir(dirPath);

      // Full file path
      const filePath = path.join(this.rootDir, normalizedFileName);

      // Write file
      await fs.writeFile(filePath, fileBuffer);

      // Get file stats
      const stats = await fs.stat(filePath);

      // Generate URL
      const url = this.getFileUrl(normalizedFileName);

      logger.info(`File uploaded to local storage: ${filePath}`);

      return {
        success: true,
        url,
        path: normalizedFileName,
        metadata: {
          originalName: path.basename(normalizedFileName),
          contentType,
          size: stats.size,
          createdAt: stats.birthtime,
          updatedAt: stats.mtime,
        },
      };
    } catch (error) {
      logger.error('Error uploading file to local storage:', error);
      return {
        success: false,
        url: '',
        path: '',
        error: `Failed to upload file: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  /**
   * Delete a file from local storage
   * @param fileName File name (with path)
   * @returns Delete result
   */
  async deleteFile(fileName: string): Promise<DeleteResult> {
    try {
      // Normalize file name to prevent directory traversal
      const normalizedFileName = fileName.replace(/\.\./g, '').replace(/^\//, '');

      // Full file path
      const filePath = path.join(this.rootDir, normalizedFileName);

      // Check if file exists
      try {
        await fs.access(filePath);
      } catch (error) {
        // File doesn't exist
        logger.warn(`File not found for deletion: ${filePath}`);
        return {
          success: true, // Consider it a success if the file doesn't exist
        };
      }

      // Delete file
      await fs.unlink(filePath);

      logger.info(`File deleted from local storage: ${filePath}`);

      return {
        success: true,
      };
    } catch (error) {
      logger.error('Error deleting file from local storage:', error);
      return {
        success: false,
        error: `Failed to delete file: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  /**
   * Get a URL for a file
   * @param fileName File name (with path)
   * @param options Options for URL generation
   * @returns File URL
   */
  getFileUrl(fileName: string, options?: { signed?: boolean; expiresIn?: number }): string {
    // Normalize file name to prevent directory traversal
    const normalizedFileName = fileName.replace(/\.\./g, '').replace(/^\//, '');

    // For local storage, we don't support signed URLs
    if (this.useRelativeUrls) {
      // Use relative URL
      return `/uploads/${normalizedFileName}`;
    } else {
      // Use absolute URL
      return `${this.baseUrl}/${normalizedFileName}`;
    }
  }

  /**
   * Check if a file exists
   * @param fileName File name (with path)
   * @returns Whether the file exists
   */
  async fileExists(fileName: string): Promise<boolean> {
    try {
      // Normalize file name to prevent directory traversal
      const normalizedFileName = fileName.replace(/\.\./g, '').replace(/^\//, '');

      // Full file path
      const filePath = path.join(this.rootDir, normalizedFileName);

      // Check if file exists
      await fs.access(filePath);

      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get file metadata
   * @param fileName File name (with path)
   * @returns File metadata
   */
  async getFileMetadata(fileName: string): Promise<FileMetadata | null> {
    try {
      // Normalize file name to prevent directory traversal
      const normalizedFileName = fileName.replace(/\.\./g, '').replace(/^\//, '');

      // Full file path
      const filePath = path.join(this.rootDir, normalizedFileName);

      // Get file stats
      const stats = await fs.stat(filePath);

      // Try to determine content type based on extension
      const ext = path.extname(normalizedFileName).toLowerCase();
      let contentType = 'application/octet-stream';

      // Simple mapping of common extensions to content types
      const contentTypeMap: Record<string, string> = {
        '.pdf': 'application/pdf',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.png': 'image/png',
        '.gif': 'image/gif',
        '.svg': 'image/svg+xml',
        '.txt': 'text/plain',
        '.html': 'text/html',
        '.css': 'text/css',
        '.js': 'application/javascript',
        '.json': 'application/json',
      };

      if (ext in contentTypeMap) {
        contentType = contentTypeMap[ext];
      }

      return {
        originalName: path.basename(normalizedFileName),
        contentType,
        size: stats.size,
        createdAt: stats.birthtime,
        updatedAt: stats.mtime,
      };
    } catch (error) {
      return null;
    }
  }
}
