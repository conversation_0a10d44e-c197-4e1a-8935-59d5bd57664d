/**
 * Client-side role-based access control (RBAC) utility
 * 
 * This module provides utilities for checking user permissions on the client side.
 */

/**
 * Available user roles in the system
 */
export enum UserRole {
  ADMIN = "ADMIN",
  ORGANIZATION_ADMIN = "ORGANIZATION_ADMIN",
  ORGANIZATION_USER = "ORGANIZATION_USER", // Regular users (default for registration)
  USER = "USER", // Legacy alias for ORGANIZATION_USER
  BROKER = "BROKER",
  SPV_USER = "SPV_USER",
}

/**
 * Permission types for different actions
 */
export enum Permission {
  // Organization permissions
  CREATE_ORGANIZATION = "create:organization",
  UPDATE_ORGANIZATION = "update:organization",
  DELETE_ORGANIZATION = "delete:organization",
  
  // User management permissions
  INVITE_USER = "invite:user",
  REMOVE_USER = "remove:user",
  UPDATE_USER_ROLE = "update:user:role",
  
  // Carbon credit permissions
  CREATE_CARBON_CREDIT = "create:carbon_credit",
  UPDATE_CARBON_CREDIT = "update:carbon_credit",
  DELETE_CARBON_CREDIT = "delete:carbon_credit",
  TOKENIZE_CARBON_CREDIT = "tokenize:carbon_credit",
  RETIRE_CARBON_CREDIT = "retire:carbon_credit",
  
  // Wallet permissions
  CREATE_WALLET = "create:wallet",
  TRANSFER_TOKENS = "transfer:tokens",
  
  // Broker permissions
  VIEW_BROKER_DASHBOARD = "view:broker_dashboard",
  MANAGE_BROKER_PROFILE = "manage:broker_profile",
  VIEW_BROKER_CLIENTS = "view:broker_clients",
  MANAGE_BROKER_CLIENTS = "manage:broker_clients",
  VIEW_BROKER_TRANSACTIONS = "view:broker_transactions",

  // Admin permissions
  VIEW_ADMIN_DASHBOARD = "view:admin_dashboard",
  MANAGE_PLATFORM = "manage:platform",
}

/**
 * Permission mapping for different roles
 */
const rolePermissions: Record<UserRole, Permission[]> = {
  [UserRole.ADMIN]: Object.values(Permission), // Admins have all permissions
  
  [UserRole.ORGANIZATION_ADMIN]: [
    Permission.UPDATE_ORGANIZATION,
    Permission.INVITE_USER,
    Permission.REMOVE_USER,
    Permission.UPDATE_USER_ROLE,
    Permission.CREATE_CARBON_CREDIT,
    Permission.UPDATE_CARBON_CREDIT,
    Permission.DELETE_CARBON_CREDIT,
    Permission.TOKENIZE_CARBON_CREDIT,
    Permission.RETIRE_CARBON_CREDIT,
    Permission.CREATE_WALLET,
    Permission.TRANSFER_TOKENS,
  ],
  
  [UserRole.ORGANIZATION_USER]: [
    Permission.TOKENIZE_CARBON_CREDIT,
    Permission.RETIRE_CARBON_CREDIT,
    Permission.TRANSFER_TOKENS,
  ],

  [UserRole.USER]: [
    Permission.TOKENIZE_CARBON_CREDIT,
    Permission.RETIRE_CARBON_CREDIT,
    Permission.TRANSFER_TOKENS,
  ],

  [UserRole.BROKER]: [
    Permission.VIEW_BROKER_DASHBOARD,
    Permission.MANAGE_BROKER_PROFILE,
    Permission.VIEW_BROKER_CLIENTS,
    Permission.MANAGE_BROKER_CLIENTS,
    Permission.VIEW_BROKER_TRANSACTIONS,
    Permission.TOKENIZE_CARBON_CREDIT,
    Permission.RETIRE_CARBON_CREDIT,
    Permission.TRANSFER_TOKENS,
  ],
};

/**
 * Check if a user has a specific permission
 * @param userRole The user's role
 * @param permission The permission to check
 * @returns True if the user has the permission, false otherwise
 */
export function hasPermission(userRole: UserRole, permission: Permission): boolean {
  return rolePermissions[userRole]?.includes(permission) || false;
}

/**
 * Check if a user can access a specific resource
 * @param userRole The user's role
 * @param userId The user's ID
 * @param userOrgId The user's organization ID
 * @param resourceOwnerId The ID of the resource owner (user or organization)
 * @param resourceType The type of resource (e.g., 'carbon_credit', 'wallet')
 * @returns True if the user can access the resource, false otherwise
 */
export function canAccessResource(
  userRole: UserRole,
  userId: string,
  userOrgId: string | null,
  resourceOwnerId: string,
  resourceType: string
): boolean {
  // Platform admins can access all resources
  if (userRole === UserRole.ADMIN) {
    return true;
  }
  
  // If the resource belongs to the user, they can access it
  if (resourceOwnerId === userId) {
    return true;
  }
  
  // If the resource belongs to the user's organization, check their role
  if (userOrgId === resourceOwnerId) {
    // Organization admins can access all organization resources
    if (userRole === UserRole.ORGANIZATION_ADMIN) {
      return true;
    }
    
    // Regular users can access some organization resources based on resource type
    if (userRole === UserRole.USER || userRole === UserRole.ORGANIZATION_USER) {
      // Define which resource types regular users can access
      const accessibleResourceTypes = ['carbon_credit', 'wallet'];
      return accessibleResourceTypes.includes(resourceType);
    }
  }
  
  return false;
}
