/**
 * Validation Schemas
 *
 * This module provides standardized validation schemas for use across the application.
 * It ensures consistent validation rules and error messages for all forms.
 */

import { z } from "zod";

// ===== Basic Validation Schemas =====

/**
 * Email validation schema
 * Validates that the input is a properly formatted email address
 */
export const emailSchema = z
  .string()
  .trim()
  .email("Please enter a valid email address")
  .min(5, "Email must be at least 5 characters")
  .max(255, "Email must be less than 255 characters");

/**
 * Simple email validation schema (less strict, for login forms)
 * Only validates that the input is a properly formatted email address
 */
export const simpleEmailSchema = z
  .string()
  .trim()
  .email("Please enter a valid email address");

/**
 * Password validation schema
 * Validates that the password meets security requirements
 */
export const passwordSchema = z
  .string()
  .min(8, "Password must be at least 8 characters")
  .max(100, "Password must be less than 100 characters")
  .regex(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
    "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character"
  );

/**
 * Simple password validation schema (less strict, for login forms)
 * Only validates that the password is not empty
 */
export const simplePasswordSchema = z
  .string()
  .min(1, "Please enter your password");

/**
 * Name validation schema
 * Validates that the name contains only valid characters and is of appropriate length
 */
export const nameSchema = z
  .string()
  .trim()
  .min(2, "Name must be at least 2 characters")
  .max(100, "Name must be less than 100 characters")
  .regex(/^[a-zA-Z\s'-]+$/, "Name can only contain letters, spaces, hyphens, and apostrophes");

/**
 * Simple name validation schema (less strict)
 * Only validates that the name is of appropriate length
 */
export const simpleNameSchema = z
  .string()
  .trim()
  .min(2, "Name must be at least 2 characters")
  .max(100, "Name must be less than 100 characters");

/**
 * Organization name validation schema
 * Validates that the organization name is of appropriate length
 */
export const organizationNameSchema = z
  .string()
  .trim()
  .min(2, "Organization name must be at least 2 characters")
  .max(100, "Organization name must be less than 100 characters");

/**
 * URL validation schema
 * Validates that the input is a properly formatted URL
 */
export const urlSchema = z
  .string()
  .trim()
  .url("Please enter a valid URL")
  .max(2048, "URL must be less than 2048 characters")
  .optional()
  .or(z.literal(""));

/**
 * Phone number validation schema
 * Validates that the input is a properly formatted phone number
 */
export const phoneNumberSchema = z
  .string()
  .trim()
  .regex(/^\+?[0-9]{10,15}$/, "Please enter a valid phone number")
  .optional()
  .or(z.literal(""));

/**
 * Country validation schema
 * Validates that the country is selected
 */
export const countrySchema = z
  .string()
  .trim()
  .min(2, "Country is required");

/**
 * Address validation schema
 * Validates that the address is of appropriate length
 */
export const addressSchema = z
  .string()
  .trim()
  .min(5, "Address must be at least 5 characters")
  .max(255, "Address must be less than 255 characters")
  .optional()
  .or(z.literal(""));

/**
 * City validation schema
 * Validates that the city is of appropriate length
 */
export const citySchema = z
  .string()
  .trim()
  .min(2, "City must be at least 2 characters")
  .max(100, "City must be less than 100 characters")
  .optional()
  .or(z.literal(""));

/**
 * State/Province validation schema
 * Validates that the state/province is of appropriate length
 */
export const stateSchema = z
  .string()
  .trim()
  .min(2, "State/Province must be at least 2 characters")
  .max(100, "State/Province must be less than 100 characters")
  .optional()
  .or(z.literal(""));

/**
 * Postal code validation schema
 * Validates that the postal code is of appropriate length
 */
export const postalCodeSchema = z
  .string()
  .trim()
  .min(3, "Postal code must be at least 3 characters")
  .max(20, "Postal code must be less than 20 characters")
  .optional()
  .or(z.literal(""));

/**
 * Industry validation schema
 * Validates that the industry is selected
 */
export const industrySchema = z
  .string()
  .trim()
  .min(2, "Industry is required");

/**
 * Company size validation schema
 * Validates that the company size is selected
 */
export const companySizeSchema = z
  .enum(["SMALL", "MEDIUM", "LARGE", "ENTERPRISE"], {
    errorMap: () => ({ message: "Please select a company size" }),
  });

/**
 * Founded year validation schema
 * Validates that the founded year is within a reasonable range
 */
export const foundedYearSchema = z
  .number()
  .int("Year must be a whole number")
  .min(1800, "Year must be 1800 or later")
  .max(new Date().getFullYear(), "Year cannot be in the future")
  .optional()
  .or(z.literal(""));

// ===== Blockchain-Related Validation Schemas =====

/**
 * Ethereum address validation schema
 * Validates that the input is a properly formatted Ethereum address
 */
export const ethereumAddressSchema = z
  .string()
  .trim()
  .regex(/^0x[a-fA-F0-9]{40}$/, "Please enter a valid Ethereum address");

/**
 * Transaction hash validation schema
 * Validates that the input is a properly formatted transaction hash
 */
export const transactionHashSchema = z
  .string()
  .trim()
  .regex(/^0x[a-fA-F0-9]{64}$/, "Please enter a valid transaction hash");

/**
 * Amount validation schema (positive number with up to 18 decimal places)
 * Validates that the amount is a positive number with up to 18 decimal places
 */
export const amountSchema = z
  .string()
  .trim()
  .regex(/^\d+(\.\d{1,18})?$/, "Please enter a valid amount with up to 18 decimal places")
  .refine((val) => parseFloat(val) > 0, "Amount must be greater than 0");

/**
 * Quantity validation schema (positive number with up to 6 decimal places)
 * Validates that the quantity is a positive number with up to 6 decimal places
 */
export const quantitySchema = z
  .number()
  .positive("Quantity must be positive")
  .multipleOf(0.000001, "Quantity can have up to 6 decimal places");

/**
 * Price validation schema (positive number with up to 6 decimal places)
 * Validates that the price is a positive number with up to 6 decimal places
 */
export const priceSchema = z
  .number()
  .positive("Price must be positive")
  .multipleOf(0.000001, "Price can have up to 6 decimal places");

/**
 * Network validation schema
 * Validates that the network is selected
 */
export const networkSchema = z
  .enum(["ETHEREUM", "POLYGON", "ARBITRUM", "OPTIMISM", "BASE"], {
    errorMap: () => ({ message: "Please select a valid network" }),
  });

/**
 * Wallet type validation schema
 * Validates that the wallet type is selected
 */
export const walletTypeSchema = z
  .enum(["SMART_WALLET", "REGULAR_WALLET"], {
    errorMap: () => ({ message: "Please select a valid wallet type" }),
  });

// ===== Composite Validation Schemas =====

/**
 * Password with confirmation validation schema
 * Validates that the password meets security requirements and matches the confirmation
 */
export const passwordWithConfirmationSchema = z.object({
  password: passwordSchema,
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
});

/**
 * Simple password with confirmation validation schema (less strict)
 * Validates that the password is of appropriate length and matches the confirmation
 */
export const simplePasswordWithConfirmationSchema = z.object({
  password: z.string().min(8, "Password must be at least 8 characters"),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
});

/**
 * Registration form validation schema
 * Validates all fields required for user registration
 */
export const registrationSchema = z.object({
  name: simpleNameSchema,
  email: emailSchema,
  // Use passwordSchema for proper validation with field-level errors
  password: passwordSchema,
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
});

/**
 * Login form validation schema
 * Validates all fields required for user login
 */
export const loginSchema = z.object({
  email: simpleEmailSchema,
  password: simplePasswordSchema,
});

/**
 * Organization form validation schema
 * Validates all fields required for organization creation/update
 */
export const organizationSchema = z.object({
  // Essential fields (required)
  name: organizationNameSchema,
  industry: industrySchema,
  size: companySizeSchema,
  country: countrySchema,

  // Optional fields that can be filled later
  description: z.string().optional(),
  website: urlSchema,
  legalName: z.string().optional(),
  registrationNumber: z.string().optional(),
  taxId: z.string().optional(),
  address: addressSchema,
  city: citySchema,
  state: stateSchema,
  postalCode: postalCodeSchema,
  phoneNumber: phoneNumberSchema,
  foundedYear: z.number().int().min(1800).max(new Date().getFullYear()).optional(),
  primaryContact: z.string().optional(),
  primaryContactEmail: z.string().email('Please enter a valid email').optional().or(z.literal("")),
  primaryContactPhone: phoneNumberSchema,
});

/**
 * Wallet creation validation schema
 * Validates all fields required for wallet creation
 */
export const walletCreationSchema = z.object({
  walletType: walletTypeSchema,
  network: networkSchema,
  securityLevel: z.enum(["STANDARD", "HIGH"]),
  testMode: z.boolean().default(true),
});

/**
 * Carbon credit validation schema
 * Validates all fields required for carbon credit creation
 */
export const carbonCreditSchema = z.object({
  name: simpleNameSchema,
  description: z.string().min(10, "Description must be at least 10 characters"),
  quantity: z.coerce.number().positive("Quantity must be positive"),
  price: z.coerce.number().positive("Price must be positive"),
  vintage: z.coerce.number().int().min(2000, "Vintage year must be 2000 or later"),
  standard: z.string().min(1, "Standard is required"),
  methodology: z.string().min(1, "Methodology is required"),
  location: z.string().optional(),
});

/**
 * Marketplace listing validation schema
 * Validates all fields required for marketplace listing creation
 */
export const marketplaceListingSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
  carbonCreditId: z.string().min(1, "Carbon credit is required"),
  quantity: z.number().positive("Quantity must be positive"),
  minPurchaseQuantity: z.number().positive("Minimum purchase quantity must be positive").default(1),
  pricingStrategy: z.enum(["FIXED", "AUCTION", "DYNAMIC", "TIERED"]).default("FIXED"),
  price: z.number().positive("Price must be positive").optional(),
  visibility: z.enum(["PUBLIC", "PRIVATE"]).default("PUBLIC"),
  featured: z.boolean().default(false),
  tags: z.array(z.string()).optional(),
});

/**
 * SPV creation validation schema (Basic onboarding)
 * Validates basic fields required for SPV creation - all optional for initial onboarding
 */
export const spvCreationSchema = z.object({
  name: z.string().min(3, "SPV name must be at least 3 characters").max(100, "SPV name must be less than 100 characters"),
  purpose: z.string().max(500, "Purpose must be less than 500 characters").optional(),
  legalStructure: z.string().max(50, "Legal structure must be less than 50 characters").optional(),
  registrationNumber: z.string().optional(),
  taxId: z.string().optional(),
  address: z.string().optional(),
  description: z.string().optional(),
  establishedDate: z.string().optional(), // Will be converted to Date on backend
  jurisdiction: z.string().optional(),

  // Basic required fields - made optional for initial onboarding
  country: z.string().optional(),
  legalEntityId: z.string().optional(),
  contact: z.string().optional(),
  projectCategories: z.string().optional(),

  // Admin email for credential sending
  adminEmail: emailSchema,
});

/**
 * SPV verification details schema
 * Validates all fields required for SPV verification process
 */
export const spvVerificationSchema = z.object({
  // Basic SPV information
  name: z.string().min(3, "SPV name must be at least 3 characters").max(100, "SPV name must be less than 100 characters"),
  purpose: z.string().max(500, "Purpose must be less than 500 characters").optional(),

  // Legal structure and registration
  legalStructure: z.string().min(1, "Legal structure is required").max(50, "Legal structure must be less than 50 characters"),
  registrationNumber: z.string().min(1, "Registration number is required").max(100, "Registration number must be less than 100 characters"),
  jurisdiction: z.string().min(1, "Jurisdiction is required").max(100, "Jurisdiction must be less than 100 characters"),
  country: z.string().min(2, "Country is required").max(100, "Country must be less than 100 characters"),

  // Indian compliance fields
  gstNumber: z.string().min(15, "GST number must be 15 characters").max(15, "GST number must be 15 characters").optional(),
  cinNumber: z.string().min(21, "CIN number must be 21 characters").max(21, "CIN number must be 21 characters").optional(),
  panNumber: z.string().min(10, "PAN number must be 10 characters").max(10, "PAN number must be 10 characters").optional(),

  // Dates
  incorporationDate: z.string().optional(), // Will be converted to Date on backend
  establishedDate: z.string().optional(),

  // Address information
  registeredAddress: z.string().min(1, "Registered address is required").max(500, "Address must be less than 500 characters"),
  address: z.string().optional(), // Additional address field

  // Contact information
  contactPersonName: z.string().min(1, "Contact person name is required").max(100, "Name must be less than 100 characters"),
  contactPersonEmail: emailSchema,
  contactPersonMobile: z.string().min(10, "Mobile number must be at least 10 digits").max(15, "Mobile number must be less than 15 digits"),
  contact: z.string().optional(), // Additional contact info

  // Banking information
  bankAccountNumber: z.string().min(1, "Bank account number is required").max(20, "Account number must be less than 20 characters"),
  ifscCode: z.string().min(11, "IFSC code must be 11 characters").max(11, "IFSC code must be 11 characters"),

  // Project and business information
  projectCategories: z.string().min(1, "Project category is required"),
  description: z.string().optional(),

  // Tax information
  taxId: z.string().optional(),
  legalEntityId: z.string().optional(),
});

/**
 * Send tokens validation schema
 * Validates all fields required for sending tokens
 */
export const sendTokensSchema = z.object({
  recipientAddress: ethereumAddressSchema,
  amount: z
    .string()
    .refine((val) => !isNaN(parseFloat(val)), { message: "Amount must be a number" })
    .refine((val) => parseFloat(val) > 0, { message: "Amount must be greater than 0" }),
  token: z.string().optional(),
  gasOption: z.enum(["standard", "fast", "instant"]),
  memo: z.string().optional(),
});

/**
 * Notification preferences validation schema
 * Validates all fields required for notification preferences
 */
export const notificationPreferencesSchema = z.object({
  emailNotifications: z.boolean(),
  inAppNotifications: z.boolean(),
  systemEmailEnabled: z.boolean(),
  systemInAppEnabled: z.boolean(),
  transactionEmailEnabled: z.boolean(),
  transactionInAppEnabled: z.boolean(),
  creditEmailEnabled: z.boolean(),
  creditInAppEnabled: z.boolean(),
  billingEmailEnabled: z.boolean(),
  billingInAppEnabled: z.boolean(),
  documentEmailEnabled: z.boolean(),
  documentInAppEnabled: z.boolean(),
  securityEmailEnabled: z.boolean(),
  securityInAppEnabled: z.boolean(),
  marketingEmailEnabled: z.boolean(),
  marketingInAppEnabled: z.boolean(),
});

/**
 * User profile validation schema
 * Validates all fields required for user profile information
 */
export const userProfileSchema = z.object({
  name: simpleNameSchema,
  email: emailSchema.optional(),
  jobTitle: z.string().optional(),
  phoneNumber: phoneNumberSchema,
  bio: z.string().max(500, "Bio must be less than 500 characters").optional(),
});

/**
 * Password change validation schema
 * Validates all fields required for changing a password
 */
export const passwordChangeSchema = z.object({
  currentPassword: z.string().min(1, "Current password is required"),
  newPassword: passwordSchema,
  confirmPassword: z.string(),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
});

/**
 * Forgot password validation schema
 * Validates the email field for requesting a password reset
 */
export const forgotPasswordSchema = z.object({
  email: simpleEmailSchema,
});

/**
 * Reset password validation schema
 * Validates the fields required for resetting a password
 */
export const resetPasswordSchema = z.object({
  password: passwordSchema,
  confirmPassword: z.string(),
  token: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
});

/**
 * Base broker schema (without refinements)
 * Used as a base for extending
 */
const baseBrokerSchema = z.object({
  name: z.string().min(3, "Broker name must be at least 3 characters").max(100, "Broker name must be less than 100 characters"),
  legalName: z.string().max(100, "Legal name must be less than 100 characters").optional(),
  description: z.string().max(1000, "Description must be less than 1000 characters").optional(),
  website: urlSchema,
  email: emailSchema,
  phoneNumber: phoneNumberSchema,
  address: addressSchema,
  city: citySchema,
  state: stateSchema,
  postalCode: postalCodeSchema,
  country: countrySchema,
  licenseNumber: z.string().max(50, "License number must be less than 50 characters").optional(),
  licenseType: z.string().max(50, "License type must be less than 50 characters").optional(),
  licenseIssuer: z.string().max(100, "License issuer must be less than 100 characters").optional(),
  licenseExpiryDate: z.string().optional(), // Will be converted to Date on backend
  registrationNumber: z.string().max(50, "Registration number must be less than 50 characters").optional(),
  taxId: z.string().max(50, "Tax ID must be less than 50 characters").optional(),
  operatingModel: z.enum(["FULL_SERVICE", "EXECUTION_ONLY", "ADVISORY_ONLY", "HYBRID"]).optional().default("FULL_SERVICE"),
  commissionRate: z.number().min(0, "Commission rate must be non-negative").max(1, "Commission rate must be less than or equal to 100%").optional().default(0.02),
  minimumCommission: z.number().min(0, "Minimum commission must be non-negative").optional(),
  maximumCommission: z.number().min(0, "Maximum commission must be non-negative").optional(),
  specializations: z.array(z.string()).optional(),
  servicesOffered: z.array(z.string()).optional(),
  targetMarkets: z.array(z.string()).optional(),
  bondAmount: z.number().min(0, "Bond amount must be non-negative").optional(),
  insuranceAmount: z.number().min(0, "Insurance amount must be non-negative").optional(),
  creditRating: z.string().max(10, "Credit rating must be less than 10 characters").optional(),
  establishedDate: z.string().optional(), // Will be converted to Date on backend
});

/**
 * Broker creation validation schema
 * Validates all fields required for broker creation
 */
export const brokerCreationSchema = baseBrokerSchema.refine((data) => {
  if (data.minimumCommission && data.maximumCommission) {
    return data.minimumCommission <= data.maximumCommission;
  }
  return true;
}, {
  message: "Minimum commission must be less than or equal to maximum commission",
  path: ["maximumCommission"],
});

/**
 * Admin broker creation validation schema
 * Extends broker creation schema with organization and user fields
 */
export const adminBrokerCreationSchema = baseBrokerSchema.extend({
  organizationId: z.string().optional(),
  userId: z.string().optional(),
}).refine((data) => {
  // Validate commission constraints
  if (data.minimumCommission && data.maximumCommission) {
    if (data.minimumCommission > data.maximumCommission) {
      return false;
    }
  }
  return true;
}, {
  message: "Minimum commission must be less than or equal to maximum commission",
  path: ["maximumCommission"],
}).refine((data) => {
  // Either organizationId or userId should be provided, but not both
  const hasOrganization = !!data.organizationId;
  const hasUser = !!data.userId;
  return hasOrganization !== hasUser; // XOR - exactly one should be true
}, {
  message: "Either organization or user must be specified, but not both",
  path: ["organizationId"],
});

/**
 * Broker update validation schema
 * Validates all fields for broker updates (all fields optional except constraints)
 */
export const brokerUpdateSchema = baseBrokerSchema.partial().extend({
  status: z.enum(["ACTIVE", "INACTIVE", "SUSPENDED", "TERMINATED"]).optional(),
  verificationStatus: z.enum(["PENDING", "VERIFIED", "REJECTED", "SUSPENDED"]).optional(),
}).refine((data) => {
  if (data.minimumCommission && data.maximumCommission) {
    return data.minimumCommission <= data.maximumCommission;
  }
  return true;
}, {
  message: "Minimum commission must be less than or equal to maximum commission",
  path: ["maximumCommission"],
});

/**
 * Admin broker update validation schema
 * Validates all fields for admin broker updates (includes organization and user fields)
 */
export const adminBrokerUpdateSchema = baseBrokerSchema.partial().extend({
  status: z.enum(["ACTIVE", "INACTIVE", "SUSPENDED", "TERMINATED"]).optional(),
  verificationStatus: z.enum(["PENDING", "VERIFIED", "REJECTED", "SUSPENDED"]).optional(),
  organizationId: z.string().optional(),
  userId: z.string().optional(),
}).refine((data) => {
  if (data.minimumCommission && data.maximumCommission) {
    return data.minimumCommission <= data.maximumCommission;
  }
  return true;
}, {
  message: "Minimum commission must be less than or equal to maximum commission",
  path: ["maximumCommission"],
});

/**
 * Base broker client schema (without refinements)
 */
const baseBrokerClientSchema = z.object({
  clientType: z.enum(["ORGANIZATION", "INDIVIDUAL"]),
  clientName: z.string().min(1, "Client name is required").max(100, "Client name must be less than 100 characters"),
  clientEmail: emailSchema,
  clientPhone: phoneNumberSchema,
  organizationId: z.string().optional(),
  userId: z.string().optional(),
  relationshipType: z.string().max(50, "Relationship type must be less than 50 characters").optional(),
  commissionRate: z.number().min(0, "Commission rate must be non-negative").max(1, "Commission rate must be less than or equal to 100%").optional(),
  contractStartDate: z.string().optional(), // Will be converted to Date on backend
  contractEndDate: z.string().optional(), // Will be converted to Date on backend
  notes: z.string().max(1000, "Notes must be less than 1000 characters").optional(),
});

/**
 * Broker client creation validation schema
 * Validates all fields required for broker client creation
 */
export const brokerClientCreationSchema = baseBrokerClientSchema.refine((data) => {
  // If clientType is ORGANIZATION, organizationId should be provided
  // If clientType is INDIVIDUAL, userId should be provided
  if (data.clientType === "ORGANIZATION") {
    return !!data.organizationId;
  } else if (data.clientType === "INDIVIDUAL") {
    return !!data.userId;
  }
  return true;
}, {
  message: "Organization ID is required for organization clients, User ID is required for individual clients",
  path: ["organizationId"],
});

/**
 * Broker client update validation schema
 * Validates all fields for broker client updates
 */
export const brokerClientUpdateSchema = baseBrokerClientSchema.partial().extend({
  status: z.enum(["ACTIVE", "INACTIVE", "SUSPENDED", "TERMINATED"]).optional(),
}).refine((data) => {
  // If clientType is provided and ORGANIZATION, organizationId should be provided
  // If clientType is provided and INDIVIDUAL, userId should be provided
  if (data.clientType === "ORGANIZATION" && data.organizationId === undefined) {
    return false;
  } else if (data.clientType === "INDIVIDUAL" && data.userId === undefined) {
    return false;
  }
  return true;
}, {
  message: "Organization ID is required for organization clients, User ID is required for individual clients",
  path: ["organizationId"],
});

/**
 * Broker query parameters validation schema
 * Validates query parameters for broker listing and filtering
 */
export const brokerQuerySchema = z.object({
  page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 10),
  search: z.string().optional(),
  organizationId: z.string().optional(),
  status: z.enum(["ACTIVE", "INACTIVE", "SUSPENDED", "TERMINATED"]).optional(),
  operatingModel: z.enum(["FULL_SERVICE", "EXECUTION_ONLY", "ADVISORY_ONLY", "HYBRID"]).optional(),
  verificationStatus: z.enum(["PENDING", "IN_REVIEW", "VERIFIED", "REJECTED"]).optional(),
  sortBy: z.enum(["name", "createdAt", "status", "organization", "commissionRate"]).optional().default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).optional().default("desc"),
});
