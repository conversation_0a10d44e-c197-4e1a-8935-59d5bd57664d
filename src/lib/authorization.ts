import { auth, getCurrentUser } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { AuditLogType } from "@prisma/client";
import { redirect } from "next/navigation";
import { cookies } from "next/headers";

/**
 * Role-based access control (RBAC) utility
 * 
 * This module provides utilities for checking user permissions and
 * enforcing access control throughout the application.
 */

/**
 * Available user roles in the system
 */
export enum UserRole {
  ADMIN = "ADMIN",
  ORGANIZATION_ADMIN = "ORGANIZATION_ADMIN",
  ORGANIZATION_USER = "ORGANIZATION_USER", // Regular users (default for registration)
  USER = "USER", // Legacy alias for ORGANIZATION_USER
  BROKER = "BROKER",
  SPV_USER = "SPV_USER",
}

/**
 * Permission types for different actions
 */
export enum Permission {
  // Organization permissions
  CREATE_ORGANIZATION = "create:organization",
  UPDATE_ORGANIZATION = "update:organization",
  DELETE_ORGANIZATION = "delete:organization",
  
  // User management permissions
  INVITE_USER = "invite:user",
  REMOVE_USER = "remove:user",
  UPDATE_USER_ROLE = "update:user:role",
  
  // Carbon credit permissions
  CREATE_CARBON_CREDIT = "create:carbon_credit",
  UPDATE_CARBON_CREDIT = "update:carbon_credit",
  DELETE_CARBON_CREDIT = "delete:carbon_credit",
  TOKENIZE_CARBON_CREDIT = "tokenize:carbon_credit",
  RETIRE_CARBON_CREDIT = "retire:carbon_credit",
  
  // Wallet permissions
  CREATE_WALLET = "create:wallet",
  TRANSFER_TOKENS = "transfer:tokens",
  
  // Broker permissions
  VIEW_BROKER_DASHBOARD = "view:broker_dashboard",
  MANAGE_BROKER_PROFILE = "manage:broker_profile",
  VIEW_BROKER_CLIENTS = "view:broker_clients",
  MANAGE_BROKER_CLIENTS = "manage:broker_clients",
  VIEW_BROKER_TRANSACTIONS = "view:broker_transactions",

  // SPV permissions
  VIEW_SPV_DASHBOARD = "view:spv_dashboard",
  READ_ASSIGNED_PROJECTS = "read:assigned_projects",
  CREATE_UNIT_LOG = "create:unit_log",
  UPDATE_UNIT_LOG_OWN = "update:unit_log:own",
  UPDATE_UNIT_LOG_ANY = "update:unit_log:any",
  SUBMIT_DATA_FOR_VERIFICATION = "submit:data_for_verification",
  VERIFY_UNIT_LOG = "verify:unit_log",
  REJECT_UNIT_LOG = "reject:unit_log",
  APPROVE_SPV_LEVEL = "approve:spv_level",
  APPROVE_ORG_LEVEL = "approve:org_level",
  MANAGE_SPV_USERS = "manage:spv_users",
  VIEW_SPV_ANALYTICS = "view:spv_analytics",
  EXPORT_PROJECT_REPORTS = "export:project_reports",

  // SPV Creation and Management permissions
  CREATE_SPV = "create:spv",
  UPDATE_SPV = "update:spv",
  DELETE_SPV = "delete:spv",
  VIEW_SPV_DETAILS = "view:spv_details",

  // SPV Verification permissions
  SUBMIT_SPV_FOR_VERIFICATION = "submit:spv_verification",
  UPDATE_SPV_VERIFICATION_DETAILS = "update:spv_verification_details",
  UPLOAD_SPV_DOCUMENTS = "upload:spv_documents",
  VIEW_SPV_DOCUMENTS = "view:spv_documents",
  DELETE_SPV_DOCUMENTS = "delete:spv_documents",

  // Admin SPV Verification permissions
  VERIFY_SPV = "verify:spv",
  REJECT_SPV = "reject:spv",
  REQUEST_SPV_MORE_INFO = "request:spv_more_info",
  VERIFY_SPV_DOCUMENTS = "verify:spv_documents",
  VIEW_SPV_VERIFICATION_QUEUE = "view:spv_verification_queue",
  MANAGE_SPV_VERIFICATION_WORKFLOW = "manage:spv_verification_workflow",

  // Admin permissions
  VIEW_ADMIN_DASHBOARD = "view:admin_dashboard",
  MANAGE_PLATFORM = "manage:platform",
}

/**
 * Permission mapping for different roles
 */
const rolePermissions: Record<UserRole, Permission[]> = {
  [UserRole.ADMIN]: Object.values(Permission), // Admins have all permissions
  
  [UserRole.ORGANIZATION_ADMIN]: [
    Permission.UPDATE_ORGANIZATION,
    Permission.INVITE_USER,
    Permission.REMOVE_USER,
    Permission.UPDATE_USER_ROLE,
    Permission.CREATE_CARBON_CREDIT,
    Permission.UPDATE_CARBON_CREDIT,
    Permission.DELETE_CARBON_CREDIT,
    Permission.TOKENIZE_CARBON_CREDIT,
    Permission.RETIRE_CARBON_CREDIT,
    Permission.CREATE_WALLET,
    Permission.TRANSFER_TOKENS,
    // SPV Management permissions for Org Admins
    Permission.CREATE_SPV,
    Permission.UPDATE_SPV,
    Permission.DELETE_SPV,
    Permission.VIEW_SPV_DETAILS,
    Permission.SUBMIT_SPV_FOR_VERIFICATION,
    Permission.UPDATE_SPV_VERIFICATION_DETAILS,
    Permission.UPLOAD_SPV_DOCUMENTS,
    Permission.VIEW_SPV_DOCUMENTS,
    Permission.DELETE_SPV_DOCUMENTS,
    Permission.MANAGE_SPV_USERS,
    Permission.APPROVE_ORG_LEVEL,
  ],
  
  [UserRole.ORGANIZATION_USER]: [
    Permission.TOKENIZE_CARBON_CREDIT,
    Permission.RETIRE_CARBON_CREDIT,
    Permission.TRANSFER_TOKENS,
  ],

  [UserRole.USER]: [
    Permission.TOKENIZE_CARBON_CREDIT,
    Permission.RETIRE_CARBON_CREDIT,
    Permission.TRANSFER_TOKENS,
  ],

  [UserRole.BROKER]: [
    Permission.VIEW_BROKER_DASHBOARD,
    Permission.MANAGE_BROKER_PROFILE,
    Permission.VIEW_BROKER_CLIENTS,
    Permission.MANAGE_BROKER_CLIENTS,
    Permission.VIEW_BROKER_TRANSACTIONS,
    Permission.TOKENIZE_CARBON_CREDIT,
    Permission.RETIRE_CARBON_CREDIT,
    Permission.TRANSFER_TOKENS,
  ],

  [UserRole.SPV_USER]: [
    Permission.VIEW_SPV_DASHBOARD,
    Permission.READ_ASSIGNED_PROJECTS,
    Permission.CREATE_UNIT_LOG,
    Permission.UPDATE_UNIT_LOG_OWN,
    Permission.SUBMIT_DATA_FOR_VERIFICATION,
    // SPV User permissions for verification
    Permission.VIEW_SPV_DETAILS,
    Permission.UPDATE_SPV_VERIFICATION_DETAILS,
    Permission.UPLOAD_SPV_DOCUMENTS,
    Permission.VIEW_SPV_DOCUMENTS,
    Permission.SUBMIT_SPV_FOR_VERIFICATION,
    // Additional permissions will be granted based on SPV role (SITE_WORKER, PROJECT_MANAGER, SPV_ADMIN)
  ],
};

/**
 * Check if a user has a specific permission
 * @param userRole The user's role
 * @param permission The permission to check
 * @returns True if the user has the permission, false otherwise
 */
export function hasPermission(userRole: UserRole, permission: Permission): boolean {
  return rolePermissions[userRole]?.includes(permission) || false;
}

/**
 * Check if the current user has a specific permission
 * @param permission The permission to check
 * @returns True if the user has the permission, false otherwise
 */
export async function checkPermission(permission: Permission): Promise<boolean> {
  const session = await auth();
  
  if (!session?.user?.role) {
    return false;
  }
  
  return hasPermission(session.user.role as UserRole, permission);
}

/**
 * Require a specific permission to access a route or component
 * If the user doesn't have the permission, they will be redirected to the dashboard
 * @param permission The permission to require
 * @param redirectTo The path to redirect to if the user doesn't have the permission
 */
export async function requirePermission(permission: Permission, redirectTo: string = "/dashboard") {
  const hasAccess = await checkPermission(permission);
  
  if (!hasAccess) {
    const session = await auth();
    const user = session?.user;
    
    // Log unauthorized access attempt
    logger.warn(`Permission denied: ${permission} for user ${user?.email || 'unknown'}`);
    
    try {
      // Record the access attempt in audit log if user is authenticated
      if (user?.id) {
        await db.auditLog.create({
          data: {
            type: AuditLogType.PERMISSION_DENIED,
            description: `Permission denied: ${permission}`,
            userId: user.id,
            organizationId: user.organizationId || undefined,
            ipAddress: cookies().get("ip")?.value,
            userAgent: cookies().get("user-agent")?.value,
          },
        });
      }
    } catch (error) {
      logger.error("Failed to log permission denied event", error);
    }
    
    redirect(redirectTo);
  }
}

/**
 * Check if the current user is an admin of the specified organization
 * @param organizationId The organization ID to check
 * @returns True if the user is an admin of the organization, false otherwise
 */
export async function isOrgAdmin(organizationId?: string): Promise<boolean> {
  const session = await auth();
  
  if (!session?.user?.role || !session?.user?.organizationId) {
    return false;
  }
  
  // If organizationId is provided, check if the user belongs to that organization
  if (organizationId && session.user.organizationId !== organizationId) {
    return false;
  }
  
  return session.user.role === UserRole.ORGANIZATION_ADMIN || session.user.role === UserRole.ADMIN;
}

/**
 * Require the user to be an admin of the specified organization
 * If the user is not an admin, they will be redirected to the dashboard
 * @param organizationId The organization ID to check
 * @param redirectTo The path to redirect to if the user is not an admin
 */
export async function requireOrgAdmin(organizationId?: string, redirectTo: string = "/dashboard") {
  const isAdmin = await isOrgAdmin(organizationId);
  
  if (!isAdmin) {
    const session = await auth();
    const user = session?.user;
    
    // Log unauthorized access attempt
    logger.warn(`Organization admin access denied for user ${user?.email || 'unknown'}`);
    
    try {
      // Record the access attempt in audit log if user is authenticated
      if (user?.id) {
        await db.auditLog.create({
          data: {
            type: AuditLogType.PERMISSION_DENIED,
            description: `Organization admin access denied${organizationId ? ` for organization ${organizationId}` : ''}`,
            userId: user.id,
            organizationId: user.organizationId || undefined,
            ipAddress: cookies().get("ip")?.value,
            userAgent: cookies().get("user-agent")?.value,
          },
        });
      }
    } catch (error) {
      logger.error("Failed to log permission denied event", error);
    }
    
    redirect(redirectTo);
  }
}

/**
 * Check if the current user is a platform admin
 * @returns True if the user is a platform admin, false otherwise
 */
export async function isPlatformAdmin(): Promise<boolean> {
  const session = await auth();
  
  if (!session?.user?.role) {
    return false;
  }
  
  return session.user.role === UserRole.ADMIN;
}

/**
 * Require the user to be a platform admin
 * If the user is not a platform admin, they will be redirected to the dashboard
 * @param redirectTo The path to redirect to if the user is not a platform admin
 */
export async function requirePlatformAdmin(redirectTo: string = "/dashboard") {
  const isAdmin = await isPlatformAdmin();
  
  if (!isAdmin) {
    const session = await auth();
    const user = session?.user;
    
    // Log unauthorized access attempt
    logger.warn(`Platform admin access denied for user ${user?.email || 'unknown'}`);
    
    try {
      // Record the access attempt in audit log if user is authenticated
      if (user?.id) {
        await db.auditLog.create({
          data: {
            type: AuditLogType.PERMISSION_DENIED,
            description: "Platform admin access denied",
            userId: user.id,
            organizationId: user.organizationId || undefined,
            ipAddress: cookies().get("ip")?.value,
            userAgent: cookies().get("user-agent")?.value,
          },
        });
      }
    } catch (error) {
      logger.error("Failed to log permission denied event", error);
    }
    
    redirect(redirectTo);
  }
}

/**
 * Check if the current user can access a specific resource
 * @param resourceOwnerId The ID of the resource owner (user or organization)
 * @param resourceType The type of resource (e.g., 'carbon_credit', 'wallet')
 * @returns True if the user can access the resource, false otherwise
 */
export async function canAccessResource(resourceOwnerId: string, resourceType: string): Promise<boolean> {
  const session = await auth();
  
  if (!session?.user?.id) {
    return false;
  }
  
  // Platform admins can access all resources
  if (session.user.role === UserRole.ADMIN) {
    return true;
  }
  
  // If the resource belongs to the user, they can access it
  if (resourceOwnerId === session.user.id) {
    return true;
  }
  
  // If the resource belongs to the user's organization, check their role
  if (session.user.organizationId === resourceOwnerId) {
    // Organization admins can access all organization resources
    if (session.user.role === UserRole.ORGANIZATION_ADMIN) {
      return true;
    }
    
    // Regular users can access some organization resources based on resource type
    if (session.user.role === UserRole.USER || session.user.role === UserRole.ORGANIZATION_USER) {
      // Define which resource types regular users can access
      const accessibleResourceTypes = ['carbon_credit', 'wallet'];
      return accessibleResourceTypes.includes(resourceType);
    }
  }
  
  return false;
}

/**
 * Require the user to be able to access a specific resource
 * If the user cannot access the resource, they will be redirected to the dashboard
 * @param resourceOwnerId The ID of the resource owner (user or organization)
 * @param resourceType The type of resource (e.g., 'carbon_credit', 'wallet')
 * @param redirectTo The path to redirect to if the user cannot access the resource
 */
export async function requireResourceAccess(
  resourceOwnerId: string, 
  resourceType: string, 
  redirectTo: string = "/dashboard"
) {
  const hasAccess = await canAccessResource(resourceOwnerId, resourceType);
  
  if (!hasAccess) {
    const session = await auth();
    const user = session?.user;
    
    // Log unauthorized access attempt
    logger.warn(`Resource access denied: ${resourceType} (${resourceOwnerId}) for user ${user?.email || 'unknown'}`);
    
    try {
      // Record the access attempt in audit log if user is authenticated
      if (user?.id) {
        await db.auditLog.create({
          data: {
            type: AuditLogType.PERMISSION_DENIED,
            description: `Resource access denied: ${resourceType} (${resourceOwnerId})`,
            userId: user.id,
            organizationId: user.organizationId || undefined,
            ipAddress: cookies().get("ip")?.value,
            userAgent: cookies().get("user-agent")?.value,
          },
        });
      }
    } catch (error) {
      logger.error("Failed to log permission denied event", error);
    }
    
    redirect(redirectTo);
  }
}

/**
 * Check if user has permission to access a specific SPV
 */
export async function hasSPVPermission(
  userId: string,
  permission: Permission,
  spvId: string
): Promise<boolean> {
  try {
    const session = await auth();
    const user = session?.user;

    if (!user || user.id !== userId) {
      return false;
    }

    // Check if user has the base permission
    const hasBasePermission = hasPermission(user.role as UserRole, permission);
    if (!hasBasePermission) {
      return false;
    }

    // Admin users have access to all SPVs
    if (user.role === UserRole.ADMIN) {
      return true;
    }

    // For SPV users, check if they belong to the specific SPV
    if (user.role === UserRole.SPV_USER) {
      const { db } = await import("@/lib/db");
      const spvUser = await db.sPVUser.findFirst({
        where: {
          userId: userId,
          spvId: spvId,
          isActive: true,
        },
      });
      return !!spvUser;
    }

    // For organization admins, check if the SPV belongs to their organization
    if (user.role === UserRole.ORGANIZATION_ADMIN && user.organizationId) {
      const { db } = await import("@/lib/db");
      const spv = await db.sPV.findFirst({
        where: {
          id: spvId,
          organizationId: user.organizationId,
        },
      });
      return !!spv;
    }

    return false;
  } catch (error) {
    logger.error("Error checking SPV permission:", error);
    return false;
  }
}

/**
 * Check if user can perform SPV verification actions
 */
export async function canVerifySPV(userId: string): Promise<boolean> {
  try {
    const session = await auth();
    const user = session?.user;

    if (!user || user.id !== userId) {
      return false;
    }

    // Only platform admins can verify SPVs
    return user.role === UserRole.ADMIN;
  } catch (error) {
    logger.error("Error checking SPV verification permission:", error);
    return false;
  }
}

/**
 * Check if user can upload documents for a specific SPV
 */
export async function canUploadSPVDocuments(userId: string, spvId: string): Promise<boolean> {
  return await hasSPVPermission(userId, Permission.UPLOAD_SPV_DOCUMENTS, spvId);
}

/**
 * Check if user can view SPV verification details
 */
export async function canViewSPVVerification(userId: string, spvId: string): Promise<boolean> {
  return await hasSPVPermission(userId, Permission.VIEW_SPV_DETAILS, spvId);
}

/**
 * Check if user can update SPV verification details
 */
export async function canUpdateSPVVerification(userId: string, spvId: string): Promise<boolean> {
  return await hasSPVPermission(userId, Permission.UPDATE_SPV_VERIFICATION_DETAILS, spvId);
}
