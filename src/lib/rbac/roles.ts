/**
 * RBAC Roles
 * 
 * This module defines predefined roles and role hierarchies.
 */

import { ALL_PERMISSIONS } from './permissions';
import { PermissionCategory } from './types';

/**
 * Role definition
 */
export interface Role {
  name: string;
  displayName: string;
  description: string;
  isSystemRole: boolean;
  parentRole?: string;
  permissions: string[]; // Permission names
}

/**
 * System roles
 */
export const SYSTEM_ROLES: Record<string, Role> = {
  // Platform-level roles
  ADMIN: {
    name: 'ADMIN',
    displayName: 'Platform Administrator',
    description: 'Full access to all platform features',
    isSystemRole: true,
    permissions: ALL_PERMISSIONS.map(p => p.name),
  },
  ORGANIZATION_ADMIN: {
    name: 'ORGANIZATION_ADMIN',
    displayName: 'Organization Administrator',
    description: 'Full access to organization features',
    isSystemRole: true,
    permissions: ALL_PERMISSIONS
      .filter(p => p.category !== PermissionCategory.ADMIN)
      .map(p => p.name),
  },
  USER: {
    name: 'USER',
    displayName: 'Regular User',
    description: 'Basic access to organization features',
    isSystemRole: true,
    permissions: [
      'read:organization',
      'read:user',
      'read:carbon_credit',
      'read:wallet',
      'read:team',
      'tokenize:carbon_credit',
      'retire:carbon_credit',
      'transfer:tokens',
      'view:transactions',
    ],
  },
  
  // Enterprise-specific roles
  DEPARTMENT_ADMIN: {
    name: 'DEPARTMENT_ADMIN',
    displayName: 'Department Administrator',
    description: 'Administrative access to a department',
    isSystemRole: true,
    parentRole: 'ORGANIZATION_ADMIN',
    permissions: [
      'read:organization',
      'update:organization',
      'invite:user',
      'read:user',
      'update:user',
      'remove:user',
      'update:user:role',
      'create:carbon_credit',
      'read:carbon_credit',
      'update:carbon_credit',
      'delete:carbon_credit',
      'list:carbon_credit',
      'unlist:carbon_credit',
      'tokenize:carbon_credit',
      'retire:carbon_credit',
      'create:wallet',
      'read:wallet',
      'update:wallet',
      'transfer:tokens',
      'view:transactions',
      'create:team',
      'read:team',
      'update:team',
      'delete:team',
      'add:team:member',
      'remove:team:member',
      'update:team:member:role',
      'create:team:role',
      'update:team:role',
      'delete:team:role',
      'view:audit:logs',
      'view:kyc:verification',
      'view:aml:checks',
      'view:compliance:documents',
      'upload:compliance:document',
      'update:department',
      'create:division',
      'update:division',
      'delete:division',
    ],
  },
  DIVISION_ADMIN: {
    name: 'DIVISION_ADMIN',
    displayName: 'Division Administrator',
    description: 'Administrative access to a division',
    isSystemRole: true,
    parentRole: 'DEPARTMENT_ADMIN',
    permissions: [
      'read:organization',
      'invite:user',
      'read:user',
      'update:user',
      'create:carbon_credit',
      'read:carbon_credit',
      'update:carbon_credit',
      'list:carbon_credit',
      'unlist:carbon_credit',
      'tokenize:carbon_credit',
      'retire:carbon_credit',
      'create:wallet',
      'read:wallet',
      'update:wallet',
      'transfer:tokens',
      'view:transactions',
      'create:team',
      'read:team',
      'update:team',
      'add:team:member',
      'remove:team:member',
      'update:team:member:role',
      'view:audit:logs',
      'view:compliance:documents',
      'upload:compliance:document',
      'update:division',
    ],
  },
};

/**
 * Predefined custom roles
 */
export const PREDEFINED_CUSTOM_ROLES: Record<string, Role> = {
  CARBON_CREDIT_MANAGER: {
    name: 'CARBON_CREDIT_MANAGER',
    displayName: 'Carbon Credit Manager',
    description: 'Manages carbon credits for the organization',
    isSystemRole: false,
    permissions: [
      'create:carbon_credit',
      'read:carbon_credit',
      'update:carbon_credit',
      'delete:carbon_credit',
      'list:carbon_credit',
      'unlist:carbon_credit',
      'tokenize:carbon_credit',
      'verify:carbon_credit',
    ],
  },
  WALLET_MANAGER: {
    name: 'WALLET_MANAGER',
    displayName: 'Wallet Manager',
    description: 'Manages wallets for the organization',
    isSystemRole: false,
    permissions: [
      'create:wallet',
      'read:wallet',
      'update:wallet',
      'delete:wallet',
      'transfer:tokens',
      'view:transactions',
    ],
  },
  TEAM_MANAGER: {
    name: 'TEAM_MANAGER',
    displayName: 'Team Manager',
    description: 'Manages teams for the organization',
    isSystemRole: false,
    permissions: [
      'create:team',
      'read:team',
      'update:team',
      'delete:team',
      'add:team:member',
      'remove:team:member',
      'update:team:member:role',
      'create:team:role',
      'update:team:role',
      'delete:team:role',
    ],
  },
  COMPLIANCE_OFFICER: {
    name: 'COMPLIANCE_OFFICER',
    displayName: 'Compliance Officer',
    description: 'Manages compliance for the organization',
    isSystemRole: false,
    permissions: [
      'view:audit:logs',
      'view:kyc:verification',
      'approve:kyc:verification',
      'reject:kyc:verification',
      'view:aml:checks',
      'perform:aml:check',
      'view:compliance:documents',
      'upload:compliance:document',
      'verify:compliance:document',
    ],
  },
  FINANCE_MANAGER: {
    name: 'FINANCE_MANAGER',
    displayName: 'Finance Manager',
    description: 'Manages financial aspects for the organization',
    isSystemRole: false,
    permissions: [
      'read:carbon_credit',
      'read:wallet',
      'view:transactions',
      'view:audit:logs',
    ],
  },
  READONLY_USER: {
    name: 'READONLY_USER',
    displayName: 'Read-Only User',
    description: 'Read-only access to organization features',
    isSystemRole: false,
    permissions: [
      'read:organization',
      'read:user',
      'read:carbon_credit',
      'read:wallet',
      'read:team',
      'view:transactions',
      'view:audit:logs',
    ],
  },
  SPV_USER: {
    name: 'SPV_USER',
    displayName: 'SPV User',
    description: 'Base role for SPV portal access with project assignment capabilities',
    isSystemRole: false,
    permissions: [
      'view:spv_dashboard',
      'read:assigned_projects',
      'read:project_data',
      'create:unit_log',
      'update:unit_log:own',
      'upload:data_files',
      'submit:data_for_verification',
    ],
  },
  SITE_WORKER: {
    name: 'SITE_WORKER',
    displayName: 'Site Worker',
    description: 'Field workers who enter raw data and submit for verification',
    isSystemRole: false,
    parentRole: 'SPV_USER',
    permissions: [
      // Inherits all SPV_USER permissions
      // No additional permissions - data entry only
    ],
  },
  PROJECT_MANAGER: {
    name: 'PROJECT_MANAGER',
    displayName: 'Project Manager',
    description: 'Manages project data and verifies site worker entries',
    isSystemRole: false,
    parentRole: 'SPV_USER',
    permissions: [
      // Inherits all SPV_USER permissions
      'verify:unit_log',
      'reject:unit_log',
      'approve:data_entry',
      'manage:project_data',
      'view:verification_queue',
      'view:spv_analytics',
      'export:project_reports',
    ],
  },
  SPV_ADMIN: {
    name: 'SPV_ADMIN',
    displayName: 'SPV Administrator',
    description: 'Full SPV management, user creation, and project oversight',
    isSystemRole: false,
    parentRole: 'PROJECT_MANAGER',
    permissions: [
      // Inherits all PROJECT_MANAGER permissions (which inherits SPV_USER)
      'review:verified_data',
      'approve:spv_level',
      'manage:spv_users',
    ],
  },
};

/**
 * Get a system role by name
 */
export function getSystemRoleByName(name: string): Role | undefined {
  return SYSTEM_ROLES[name];
}

/**
 * Get a predefined custom role by name
 */
export function getPredefinedCustomRoleByName(name: string): Role | undefined {
  return PREDEFINED_CUSTOM_ROLES[name];
}

/**
 * Get all roles (system and predefined custom)
 */
export function getAllRoles(): Role[] {
  return [...Object.values(SYSTEM_ROLES), ...Object.values(PREDEFINED_CUSTOM_ROLES)];
}

/**
 * Get role hierarchy
 * Returns an array of parent role names, from immediate parent to root
 */
export function getRoleHierarchy(roleName: string): string[] {
  const hierarchy: string[] = [];
  let currentRole = SYSTEM_ROLES[roleName] || PREDEFINED_CUSTOM_ROLES[roleName];
  
  while (currentRole?.parentRole) {
    hierarchy.push(currentRole.parentRole);
    currentRole = SYSTEM_ROLES[currentRole.parentRole] || PREDEFINED_CUSTOM_ROLES[currentRole.parentRole];
  }
  
  return hierarchy;
}

/**
 * Get inherited permissions for a role
 * Returns all permissions including those inherited from parent roles
 */
export function getInheritedPermissions(roleName: string): string[] {
  const role = SYSTEM_ROLES[roleName] || PREDEFINED_CUSTOM_ROLES[roleName];
  if (!role) return [];
  
  const permissions = [...role.permissions];
  const hierarchy = getRoleHierarchy(roleName);
  
  for (const parentRoleName of hierarchy) {
    const parentRole = SYSTEM_ROLES[parentRoleName] || PREDEFINED_CUSTOM_ROLES[parentRoleName];
    if (parentRole) {
      permissions.push(...parentRole.permissions);
    }
  }
  
  // Remove duplicates
  return [...new Set(permissions)];
}
