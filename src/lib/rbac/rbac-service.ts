/**
 * RBAC Service
 *
 * This module provides the main RBAC service for checking permissions.
 */

import { db } from '@/lib/db';
import { PermissionContext, PermissionCheckResult } from './types';
import { checkDirectPermission, logPermissionUsage } from './permission-service';
import { checkRolePermission } from './role-service';
import { checkResourcePermission } from './resource-permissions';
import { checkTeamPermission } from './team-permissions';
import { checkTemporaryPermission } from './permission-service';
import { logger } from '@/lib/logger';
import { initializeRbacSystem, isRbacInitialized } from './init-rbac';

/**
 * Check if a user has a permission
 */
export async function hasPermission(
  permissionName: string,
  context: PermissionContext,
  options: {
    logUsage?: boolean;
    action?: string;
    metadata?: Record<string, any>;
    ipAddress?: string;
    userAgent?: string;
  } = {}
): Promise<boolean> {
  try {
    // First check tenant isolation for resource access
    if (context.resourceType && context.resourceId) {
      const { canAccessResource } = await import('@/lib/tenant-isolation');
      const hasAccess = await canAccessResource(
        context.userId,
        context.resourceType,
        context.resourceId
      );

      if (!hasAccess) {
        logger.warn(`Tenant isolation: User ${context.userId} attempted to access resource ${context.resourceType}:${context.resourceId} from another organization`);

        // Log the access attempt if requested
        if (options.logUsage) {
          await logPermissionUsage(
            context.userId,
            permissionName,
            false,
            context.resourceType,
            context.resourceId,
            options.action,
            { ...options.metadata, reason: 'tenant_isolation' },
            options.ipAddress,
            options.userAgent
          );
        }

        return false;
      }
    }

    // If organization context is provided, verify user belongs to that organization
    if (context.organizationId) {
      const { canAccessOrganization } = await import('@/lib/tenant-isolation');
      const hasOrgAccess = await canAccessOrganization(
        context.userId,
        context.organizationId
      );

      if (!hasOrgAccess) {
        logger.warn(`Tenant isolation: User ${context.userId} attempted to access organization ${context.organizationId} they don't belong to`);

        // Log the access attempt if requested
        if (options.logUsage) {
          await logPermissionUsage(
            context.userId,
            permissionName,
            false,
            'organization',
            context.organizationId,
            options.action,
            { ...options.metadata, reason: 'tenant_isolation' },
            options.ipAddress,
            options.userAgent
          );
        }

        return false;
      }
    }

    // Proceed with normal permission check
    const result = await checkPermission(permissionName, context);

    // Log permission usage if requested
    if (options.logUsage) {
      await logPermissionUsage(
        context.userId,
        permissionName,
        result.granted,
        context.resourceType,
        context.resourceId,
        options.action,
        options.metadata,
        options.ipAddress,
        options.userAgent
      );
    }

    return result.granted;
  } catch (error) {
    logger.error('Error checking permission:', error);
    return false;
  }
}

/**
 * Check if a user has a permission and get detailed result
 */
export async function checkPermission(
  permissionName: string,
  context: PermissionContext
): Promise<PermissionCheckResult> {
  try {
    // Check if the permission exists
    const permission = await db.permission.findUnique({
      where: { name: permissionName },
    });

    if (!permission) {
      logger.warn(`Permission not found: ${permissionName}`);
      return { granted: false };
    }

    // Check temporary permissions first (highest priority)
    const tempResult = await checkTemporaryPermission(permissionName, context);
    if (tempResult.granted) {
      return tempResult;
    }

    // Check direct permission grants
    const directResult = await checkDirectPermission(permissionName, context);
    if (directResult.granted) {
      return directResult;
    }

    // Check role-based permissions
    const roleResult = await checkRolePermission(permissionName, context);
    if (roleResult.granted) {
      return roleResult;
    }

    // Check team-based permissions
    const teamResult = await checkTeamPermission(permissionName, context);
    if (teamResult.granted) {
      return teamResult;
    }

    // Check resource-based permissions
    if (context.resourceType && context.resourceId) {
      const resourceResult = await checkResourcePermission(permissionName, context);
      if (resourceResult.granted) {
        return resourceResult;
      }
    }

    return { granted: false };
  } catch (error) {
    logger.error('Error checking permission:', error);
    return { granted: false };
  }
}

/**
 * Re-export RBAC initialization functions
 */
export { initializeRbacSystem, isRbacInitialized } from './init-rbac';

/**
 * Require a permission (throws an error if not granted)
 */
export async function requirePermission(
  permissionName: string,
  context: PermissionContext,
  options: {
    logUsage?: boolean;
    action?: string;
    metadata?: Record<string, any>;
    ipAddress?: string;
    userAgent?: string;
  } = {}
): Promise<void> {
  const granted = await hasPermission(permissionName, context, options);

  if (!granted) {
    throw new Error(`Permission denied: ${permissionName}`);
  }
}

/**
 * Get all permissions for a user
 */
export async function getUserPermissions(userId: string): Promise<string[]> {
  try {
    const permissions = new Set<string>();

    // Get user's system role
    const user = await db.user.findUnique({
      where: { id: userId },
      select: { role: true },
    });

    if (!user) {
      return [];
    }

    // Get permissions from system role
    const systemRolePermissions = await db.permission.findMany({
      where: {
        rolePermissions: {
          some: {
            role: {
              name: user.role,
              isSystemRole: true,
            },
          },
        },
      },
      select: { name: true },
    });

    systemRolePermissions.forEach(p => permissions.add(p.name));

    // Get permissions from custom roles
    const customRolePermissions = await db.permission.findMany({
      where: {
        rolePermissions: {
          some: {
            role: {
              userRoles: {
                some: {
                  userId,
                  OR: [
                    { expiresAt: null },
                    { expiresAt: { gt: new Date() } },
                  ],
                },
              },
            },
          },
        },
      },
      select: { name: true },
    });

    customRolePermissions.forEach(p => permissions.add(p.name));

    // Get direct permission grants
    const directPermissions = await db.permission.findMany({
      where: {
        permissionGrants: {
          some: {
            userId,
            OR: [
              { expiresAt: null },
              { expiresAt: { gt: new Date() } },
            ],
          },
        },
      },
      select: { name: true },
    });

    directPermissions.forEach(p => permissions.add(p.name));

    // Get temporary permissions
    const temporaryPermissions = await db.permission.findMany({
      where: {
        tempPermissions: {
          some: {
            userId,
            expiresAt: { gt: new Date() },
          },
        },
      },
      select: { name: true },
    });

    temporaryPermissions.forEach(p => permissions.add(p.name));

    return Array.from(permissions);
  } catch (error) {
    logger.error('Error getting user permissions:', error);
    return [];
  }
}

/**
 * Get all roles for a user
 */
export async function getUserRoles(userId: string): Promise<string[]> {
  try {
    const roles = new Set<string>();

    // Get user's system role
    const user = await db.user.findUnique({
      where: { id: userId },
      select: { role: true },
    });

    if (!user) {
      return [];
    }

    // Add system role
    roles.add(user.role);

    // Get custom roles
    const customRoles = await db.customRole.findMany({
      where: {
        userRoles: {
          some: {
            userId,
            OR: [
              { expiresAt: null },
              { expiresAt: { gt: new Date() } },
            ],
          },
        },
      },
      select: { name: true },
    });

    customRoles.forEach(r => roles.add(r.name));

    return Array.from(roles);
  } catch (error) {
    logger.error('Error getting user roles:', error);
    return [];
  }
}


