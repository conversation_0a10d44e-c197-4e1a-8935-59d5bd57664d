/**
 * Utility functions for determining verification status based on user role and authority
 */

import { db } from "@/lib/db";
import { logger } from "@/lib/logger";

/**
 * Determine the appropriate verification status based on user role and authority
 * @param userId - The ID of the user entering the data
 * @param projectId - The ID of the project the data is being entered for
 * @returns Promise<string> - The appropriate verification status
 */
export async function determineVerificationStatus(
  userId: string, 
  projectId: string
): Promise<string> {
  try {
    // Get user details
    const user = await db.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        role: true,
        organizationId: true,
        spvUser: {
          select: {
            id: true,
            role: true,
            isActive: true,
            projectAssignments: {
              where: {
                projectId: projectId,
                isActive: true
              },
              select: {
                projectId: true
              }
            }
          }
        }
      }
    });

    if (!user) {
      logger.warn(`User ${userId} not found for verification status determination`);
      return "DRAFT";
    }

    // Get project details
    const project = await db.project.findUnique({
      where: { id: projectId },
      select: {
        id: true,
        organizationId: true,
        spvId: true
      }
    });

    if (!project) {
      logger.warn(`Project ${projectId} not found for verification status determination`);
      return "DRAFT";
    }

    // Determine verification status based on role hierarchy
    switch (user.role) {
      case "ADMIN":
        // Platform admins have highest authority - data is automatically verified
        logger.info(`Admin user ${userId} entering data - status: VERIFIED`);
        return "VERIFIED";

      case "ORGANIZATION_ADMIN":
        // Organization admins have authority over their organization's projects
        if (user.organizationId === project.organizationId) {
          logger.info(`Organization admin ${userId} entering data for own org - status: VERIFIED`);
          return "VERIFIED";
        } else {
          logger.warn(`Organization admin ${userId} entering data for different org - status: DRAFT`);
          return "DRAFT";
        }

      case "SPV_USER":
        // SPV users have different authority levels based on their SPV role
        if (!user.spvUser || !user.spvUser.isActive) {
          logger.warn(`SPV user ${userId} not found or inactive - status: DRAFT`);
          return "DRAFT";
        }

        // Check if user has access to this project
        const hasProjectAccess = user.spvUser.projectAssignments.length > 0;
        if (!hasProjectAccess) {
          logger.warn(`SPV user ${userId} has no access to project ${projectId} - status: DRAFT`);
          return "DRAFT";
        }

        // Determine status based on SPV role - hierarchical verification flow
        switch (user.spvUser.role) {
          case "SPV_ADMIN":
            // SPV Admin data goes directly to SPV_ADMIN_VERIFIED status
            logger.info(`SPV Admin ${userId} entering data - status: SPV_ADMIN_VERIFIED`);
            return "SPV_ADMIN_VERIFIED";

          case "PROJECT_MANAGER":
            // Project Manager data goes to PM_VERIFIED status
            logger.info(`Project Manager ${userId} entering data - status: PM_VERIFIED`);
            return "PM_VERIFIED";

          case "SITE_WORKER":
            // Site Worker data starts as DRAFT
            logger.info(`Site Worker ${userId} entering data - status: DRAFT`);
            return "DRAFT";

          default:
            logger.warn(`Unknown SPV role ${user.spvUser.role} for user ${userId} - status: DRAFT`);
            return "DRAFT";
        }

      case "USER":
        // Regular users have lowest authority
        logger.info(`Regular user ${userId} entering data - status: DRAFT`);
        return "DRAFT";

      default:
        logger.warn(`Unknown user role ${user.role} for user ${userId} - status: DRAFT`);
        return "DRAFT";
    }

  } catch (error) {
    logger.error("Error determining verification status", {
      userId,
      projectId,
      error: error instanceof Error ? error.message : String(error)
    });
    // Default to DRAFT on error
    return "DRAFT";
  }
}

/**
 * Get verification status display information
 * @param status - The verification status
 * @returns Object with display information
 */
export function getVerificationStatusInfo(status: string) {
  const statusInfo = {
    "DRAFT": {
      label: "Draft",
      description: "Data entered but not yet submitted for verification",
      color: "gray",
      variant: "outline" as const,
      icon: "📝",
      className: "bg-gray-50 text-gray-700 border-gray-200"
    },
    "SUBMITTED_FOR_VERIFICATION": {
      label: "Submitted",
      description: "Data submitted for verification by higher authority",
      color: "yellow",
      variant: "outline" as const,
      icon: "📤",
      className: "bg-yellow-50 text-yellow-700 border-yellow-200"
    },
    "PM_VERIFIED": {
      label: "PM Verified",
      description: "Data verified by Project Manager, pending SPV Admin approval",
      color: "blue",
      variant: "outline" as const,
      icon: "👤",
      className: "bg-blue-50 text-blue-700 border-blue-200"
    },
    "SPV_ADMIN_VERIFIED": {
      label: "SPV Admin Verified",
      description: "Data verified by SPV Admin, pending final organization approval",
      color: "purple",
      variant: "outline" as const,
      icon: "🏢",
      className: "bg-purple-50 text-purple-700 border-purple-200"
    },
    "VERIFIED": {
      label: "Verified",
      description: "Data fully verified and approved by organization admin",
      color: "green",
      variant: "outline" as const,
      icon: "✅",
      className: "bg-green-50 text-green-700 border-green-200"
    },
    "SPV_APPROVED": {
      label: "SPV Approved",
      description: "Data approved by SPV admin, pending organization approval",
      color: "blue",
      variant: "outline" as const,
      icon: "🔵",
      className: "bg-blue-50 text-blue-700 border-blue-200"
    },
    "REJECTED": {
      label: "Rejected",
      description: "Data rejected and requires revision",
      color: "red",
      variant: "destructive" as const,
      icon: "❌",
      className: "bg-red-50 text-red-700 border-red-200"
    }
  };

  return statusInfo[status] || statusInfo["DRAFT"];
}

/**
 * Check if user can modify verification status
 * @param userId - The user ID
 * @param currentStatus - Current verification status
 * @param targetStatus - Target verification status
 * @param projectId - Project ID
 * @returns Promise<boolean> - Whether the user can perform this status change
 */
export async function canModifyVerificationStatus(
  userId: string,
  currentStatus: string,
  targetStatus: string,
  projectId: string
): Promise<boolean> {
  try {
    const user = await db.user.findUnique({
      where: { id: userId },
      select: {
        role: true,
        organizationId: true,
        spvUser: {
          select: {
            role: true,
            isActive: true,
            projectAssignments: {
              where: {
                projectId: projectId,
                isActive: true
              }
            }
          }
        }
      }
    });

    if (!user) return false;

    const project = await db.project.findUnique({
      where: { id: projectId },
      select: { organizationId: true }
    });

    if (!project) return false;

    // Platform admins can do anything
    if (user.role === "ADMIN") return true;

    // Organization admins can do anything within their organization
    if (user.role === "ORGANIZATION_ADMIN" && user.organizationId === project.organizationId) {
      return true;
    }

    // SPV users have limited modification rights
    if (user.role === "SPV_USER" && user.spvUser?.isActive) {
      const hasProjectAccess = user.spvUser.projectAssignments.length > 0;
      if (!hasProjectAccess) return false;

      // SPV admins can approve to SPV_APPROVED
      if (user.spvUser.role === "SPV_ADMIN") {
        return targetStatus === "SPV_APPROVED" || targetStatus === "REJECTED";
      }

      // Project managers can submit for verification
      if (user.spvUser.role === "PROJECT_MANAGER") {
        return targetStatus === "SUBMITTED_FOR_VERIFICATION" || targetStatus === "REJECTED";
      }

      // Site workers can only create drafts
      if (user.spvUser.role === "SITE_WORKER") {
        return targetStatus === "DRAFT";
      }
    }

    return false;
  } catch (error) {
    logger.error("Error checking verification status modification rights", {
      userId,
      currentStatus,
      targetStatus,
      projectId,
      error: error instanceof Error ? error.message : String(error)
    });
    return false;
  }
}
