import {
  BarChart3,
  Building,
  Building2,
  CreditCard,
  FileText,
  Home,
  Leaf,
  Settings,
  ShoppingCart,
  Users,
  Wallet,
  Shield,
  FileCheck,
  Handshake,
  UserCog,
  Key,
  UserCheck,
  Activity,
} from "lucide-react";

export const navigationConfig = {
  mainNav: [
    {
      title: "Dashboard",
      items: [
        {
          title: "Overview",
          href: "/dashboard",
          icon: Home,
          description: "Get an overview of your carbon activities",
        },
        {
          title: "Analytics",
          href: "/analytics",
          icon: BarChart3,
          description: "View detailed analytics and reports",
        },
      ],
    },
    {
      title: "Carbon Management",
      items: [
        {
          title: "Projects",
          href: "/dashboard/projects",
          icon: Leaf,
          description: "Manage your carbon projects",
        },
        {
          title: "Credits",
          href: "/dashboard/carbon-credits",
          icon: CreditCard,
          description: "View and manage your carbon credits",
        },
        {
          title: "Marketplace",
          href: "/dashboard/marketplace",
          icon: ShoppingCart,
          description: "Buy and sell carbon credits",
        },
      ],
    },
    {
      title: "Finance",
      items: [
        {
          title: "Wallets",
          href: "/wallets",
          icon: Wallet,
          description: "Manage your wallets and funds",
        },
        {
          title: "Transactions",
          href: "/transactions",
          icon: CreditCard,
          description: "View your transaction history",
        },
        {
          title: "Invoices",
          href: "/invoices",
          icon: FileText,
          description: "View and manage invoices",
        },
      ],
    },
    {
      title: "Compliance",
      items: [
        {
          title: "Dashboard",
          href: "/compliance",
          icon: Shield,
          description: "Compliance overview",
        },
        {
          title: "KYC Verification",
          href: "/compliance/kyc",
          icon: Users,
          description: "Manage KYC verification",
        },
        {
          title: "AML Monitoring",
          href: "/compliance/aml",
          icon: Shield,
          description: "Anti-money laundering monitoring",
        },
        {
          title: "Document Management",
          href: "/compliance/documents",
          icon: FileText,
          description: "Manage compliance documents",
        },
        {
          title: "Carbon Verification",
          href: "/compliance/carbon-verification",
          icon: FileCheck,
          description: "Verify carbon credits and projects",
        },
      ],
    },
    {
      title: "Organization",
      items: [
        {
          title: "Team",
          href: "/organization/team",
          icon: Users,
          description: "Manage your team members",
        },
        {
          title: "SPV Management",
          href: "/dashboard/spv",
          icon: Building2,
          description: "Manage Special Purpose Vehicles",
        },
        {
          title: "Broker Management",
          href: "/dashboard/brokers",
          icon: Handshake,
          description: "Manage brokers and relationships",
        },
        {
          title: "Settings",
          href: "/organization/settings",
          icon: Building,
          description: "Manage organization settings",
        },
      ],
    },
    {
      title: "Role & Access Management",
      items: [
        {
          title: "Users",
          href: "/rbac/users",
          icon: Users,
          description: "Manage organization users and their access",
          roles: ["ORGANIZATION_ADMIN"],
        },
        {
          title: "Roles",
          href: "/rbac/roles",
          icon: UserCog,
          description: "Create and manage custom roles",
          roles: ["ORGANIZATION_ADMIN"],
        },
        {
          title: "Permissions",
          href: "/rbac/permissions",
          icon: Key,
          description: "View and manage permissions",
          roles: ["ORGANIZATION_ADMIN"],
        },
        {
          title: "Audit Log",
          href: "/rbac/audit",
          icon: Activity,
          description: "View access and permission changes",
          roles: ["ORGANIZATION_ADMIN"],
        },
      ],
    },
    {
      title: "Settings",
      items: [
        {
          title: "Account",
          href: "/settings/account",
          icon: Settings,
          description: "Manage your account settings",
        },
        {
          title: "Preferences",
          href: "/settings/preferences",
          icon: Settings,
          description: "Set your preferences",
        },
      ],
    },
  ],
};
