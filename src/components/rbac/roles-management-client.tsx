"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  UserCog,
  Plus,
  MoreHorizontal,
  Edit,
  Trash2,
  Users,
  Key,
  Shield,
  Calendar,
  Filter,
  Search,
  Eye,
  ArrowRight,
  Layers,
} from "lucide-react";
import { toast } from "sonner";
import { CreateRoleDialog } from "@/components/rbac/create-role-dialog";
import { EditRoleDialog } from "@/components/rbac/edit-role-dialog";

interface Permission {
  name: string;
  displayName: string;
  description?: string;
  category: string;
}

interface Role {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  isSystemRole: boolean;
  roleType: 'system' | 'predefined' | 'custom';
  parentRole?: {
    id: string;
    name: string;
    displayName: string;
  };
  childRoles: Array<{
    id: string;
    name: string;
    displayName: string;
  }>;
  permissions: Permission[];
  userCount: number;
  users: Array<{
    name: string;
    email: string;
  }>;
  createdAt: string;
  updatedAt: string;
  canEdit: boolean;
  canDelete: boolean;
  canInstantiate?: boolean;
}

interface PermissionWithSource extends Permission {
  source: 'direct' | 'inherited';
  inheritedFrom?: string;
}

export function RolesManagementClient() {
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showPermissionsDialog, setShowPermissionsDialog] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [roleTypeFilter, setRoleTypeFilter] = useState<string>("all");
  const [allPermissions, setAllPermissions] = useState<PermissionWithSource[]>([]);

  const fetchRoles = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/rbac/roles");
      if (!response.ok) {
        throw new Error("Failed to fetch roles");
      }
      const data = await response.json();
      setRoles(data.data);
    } catch (error) {
      console.error("Error fetching roles:", error);
      toast.error("Failed to fetch roles");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRoles();
  }, []);

  // Filter and search roles
  const filteredRoles = roles.filter(role => {
    const matchesSearch = role.displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (role.description?.toLowerCase().includes(searchTerm.toLowerCase()) ?? false);

    const matchesFilter = roleTypeFilter === "all" || role.roleType === roleTypeFilter;

    return matchesSearch && matchesFilter;
  });

  // Group roles by type for better organization
  const groupedRoles = {
    system: filteredRoles.filter(role => role.roleType === 'system'),
    predefined: filteredRoles.filter(role => role.roleType === 'predefined'),
    custom: filteredRoles.filter(role => role.roleType === 'custom'),
  };

  const handleDeleteRole = async () => {
    if (!selectedRole) return;

    try {
      const response = await fetch(`/api/rbac/roles/${selectedRole.id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to delete role");
      }

      toast.success("Role deleted successfully");
      setShowDeleteDialog(false);
      setSelectedRole(null);
      fetchRoles();
    } catch (error) {
      console.error("Error deleting role:", error);
      toast.error(error instanceof Error ? error.message : "Failed to delete role");
    }
  };



  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const groupPermissionsByCategory = (permissions: Permission[]) => {
    return permissions.reduce((acc, permission) => {
      const category = permission.category;
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(permission);
      return acc;
    }, {} as Record<string, Permission[]>);
  };

  // Function to get all permissions including inherited ones
  const getAllPermissionsWithInheritance = async (role: Role): Promise<PermissionWithSource[]> => {
    const allPerms: PermissionWithSource[] = [];

    // Add direct permissions
    role.permissions.forEach(perm => {
      allPerms.push({
        ...perm,
        source: 'direct'
      });
    });

    // Add inherited permissions if role has a parent
    if (role.parentRole) {
      try {
        // For system and predefined roles, we need to fetch the parent role's permissions
        // This is a simplified approach - in a real implementation, you'd want to
        // recursively fetch all parent permissions
        const response = await fetch(`/api/rbac/roles/${role.parentRole.id}`);
        if (response.ok) {
          const parentRoleData = await response.json();
          parentRoleData.data.permissions.forEach((perm: Permission) => {
            // Only add if not already present as direct permission
            if (!allPerms.some(p => p.name === perm.name)) {
              allPerms.push({
                ...perm,
                source: 'inherited',
                inheritedFrom: role.parentRole!.displayName
              });
            }
          });
        }
      } catch (error) {
        console.error('Error fetching parent role permissions:', error);
      }
    }

    return allPerms;
  };

  const handleViewAllPermissions = async (role: Role) => {
    const permissions = await getAllPermissionsWithInheritance(role);
    setAllPermissions(permissions);
    setSelectedRole(role);
    setShowPermissionsDialog(true);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Roles</h2>
          <p className="text-muted-foreground">
            Manage system roles, predefined templates, and custom roles
          </p>
        </div>
        <Button onClick={() => setShowCreateDialog(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Create Role
        </Button>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search roles by name, description, or permissions..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
          {searchTerm && (
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
              onClick={() => setSearchTerm("")}
            >
              ×
            </Button>
          )}
        </div>
        <Select value={roleTypeFilter} onValueChange={setRoleTypeFilter}>
          <SelectTrigger className="w-full sm:w-48">
            <Filter className="h-4 w-4 mr-2" />
            <SelectValue placeholder="Filter by type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Roles</SelectItem>
            <SelectItem value="system">System Roles</SelectItem>
            <SelectItem value="predefined">Predefined Templates</SelectItem>
            <SelectItem value="custom">Custom Roles</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Role Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Shield className="h-4 w-4 text-blue-500" />
              <div>
                <p className="text-sm font-medium">System Roles</p>
                <p className="text-2xl font-bold">{groupedRoles.system.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Key className="h-4 w-4 text-green-500" />
              <div>
                <p className="text-sm font-medium">Templates</p>
                <p className="text-2xl font-bold">{groupedRoles.predefined.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <UserCog className="h-4 w-4 text-purple-500" />
              <div>
                <p className="text-sm font-medium">Custom Roles</p>
                <p className="text-2xl font-bold">{groupedRoles.custom.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-orange-500" />
              <div>
                <p className="text-sm font-medium">Total Users</p>
                <p className="text-2xl font-bold">{roles.reduce((sum, role) => sum + role.userCount, 0)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Roles Grid */}
      {loading ? (
        <div className="text-center py-8">Loading roles...</div>
      ) : filteredRoles.length === 0 ? (
        <div className="text-center py-8">
          <UserCog className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">No roles found</h3>
          <p className="text-muted-foreground">
            {searchTerm || roleTypeFilter !== "all"
              ? "Try adjusting your search or filter criteria."
              : "Create your first custom role to get started."
            }
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredRoles.map((role) => {
            const getRoleTypeColor = (type: string) => {
              switch (type) {
                case 'system': return 'bg-blue-100 text-blue-800 border-blue-200';
                case 'predefined': return 'bg-green-100 text-green-800 border-green-200';
                case 'custom': return 'bg-purple-100 text-purple-800 border-purple-200';
                default: return 'bg-gray-100 text-gray-800 border-gray-200';
              }
            };

            const getRoleTypeIcon = (type: string) => {
              switch (type) {
                case 'system': return <Shield className="h-3 w-3" />;
                case 'predefined': return <Key className="h-3 w-3" />;
                case 'custom': return <UserCog className="h-3 w-3" />;
                default: return <UserCog className="h-3 w-3" />;
              }
            };

            return (
            <Card key={role.id} className="relative">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="space-y-1">
                    <CardTitle className="flex items-center gap-2">
                      {getRoleTypeIcon(role.roleType)}
                      {role.displayName}
                      <Badge
                        variant="outline"
                        className={`text-xs ${getRoleTypeColor(role.roleType)}`}
                      >
                        {role.roleType.charAt(0).toUpperCase() + role.roleType.slice(1)}
                      </Badge>
                    </CardTitle>
                    <CardDescription className="line-clamp-2">
                      {role.description || "No description provided"}
                    </CardDescription>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      {role.canEdit && (
                        <DropdownMenuItem
                          onClick={() => {
                            setSelectedRole(role);
                            setShowEditDialog(true);
                          }}
                        >
                          <Edit className="h-4 w-4 mr-2" />
                          Edit Role
                        </DropdownMenuItem>
                      )}
                      {role.canDelete && (
                        <>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => {
                              setSelectedRole(role);
                              setShowDeleteDialog(true);
                            }}
                            className="text-destructive"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete Role
                          </DropdownMenuItem>
                        </>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Stats */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center gap-2 text-sm">
                    <Users className="h-4 w-4 text-muted-foreground" />
                    <span>{role.userCount} users</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <Key className="h-4 w-4 text-muted-foreground" />
                    <span>
                      {role.permissions.length} direct
                      {role.parentRole && " + inherited"}
                    </span>
                  </div>
                </div>

                {/* Permissions Preview */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h4 className="text-sm font-medium">Permissions</h4>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleViewAllPermissions(role)}
                      className="h-6 px-2 text-xs"
                    >
                      <Eye className="h-3 w-3 mr-1" />
                      View All
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {Object.entries(groupPermissionsByCategory(role.permissions))
                      .slice(0, 3)
                      .map(([category, perms]) => (
                        <Badge key={category} variant="outline" className="text-xs">
                          {category} ({perms.length})
                        </Badge>
                      ))}
                    {Object.keys(groupPermissionsByCategory(role.permissions)).length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{Object.keys(groupPermissionsByCategory(role.permissions)).length - 3} more
                      </Badge>
                    )}
                  </div>
                  {role.parentRole && (
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <Layers className="h-3 w-3" />
                      <span>+ Inherits from {role.parentRole.displayName}</span>
                    </div>
                  )}
                </div>

                {/* Hierarchy */}
                {role.parentRole && (
                  <div className="space-y-1">
                    <h4 className="text-sm font-medium">Parent Role</h4>
                    <Badge variant="outline" className="text-xs">
                      {role.parentRole.displayName}
                    </Badge>
                  </div>
                )}

                {role.childRoles.length > 0 && (
                  <div className="space-y-1">
                    <h4 className="text-sm font-medium">Child Roles</h4>
                    <div className="flex flex-wrap gap-1">
                      {role.childRoles.slice(0, 2).map((childRole) => (
                        <Badge key={childRole.id} variant="outline" className="text-xs">
                          {childRole.displayName}
                        </Badge>
                      ))}
                      {role.childRoles.length > 2 && (
                        <Badge variant="outline" className="text-xs">
                          +{role.childRoles.length - 2} more
                        </Badge>
                      )}
                    </div>
                  </div>
                )}

                {/* Created Date */}
                <div className="flex items-center gap-2 text-xs text-muted-foreground pt-2 border-t">
                  <Calendar className="h-3 w-3" />
                  Created {formatDate(role.createdAt)}
                </div>
              </CardContent>
            </Card>
            );
          })}
        </div>
      )}



      {/* Dialogs */}
      <CreateRoleDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        onSuccess={() => {
          fetchRoles();
          setShowCreateDialog(false);
        }}
      />

      {selectedRole && (
        <>
          <EditRoleDialog
            open={showEditDialog}
            onOpenChange={setShowEditDialog}
            role={selectedRole}
            onSuccess={() => {
              fetchRoles();
              setShowEditDialog(false);
              setSelectedRole(null);
            }}
          />

          <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Delete Role</DialogTitle>
                <DialogDescription>
                  Are you sure you want to delete the role "{selectedRole.displayName}"?
                  This action cannot be undone.
                  {selectedRole.userCount > 0 && (
                    <span className="block mt-2 text-destructive">
                      This role is assigned to {selectedRole.userCount} user(s) and cannot be deleted.
                    </span>
                  )}
                </DialogDescription>
              </DialogHeader>
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => setShowDeleteDialog(false)}
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleDeleteRole}
                  disabled={selectedRole.userCount > 0}
                >
                  Delete Role
                </Button>
              </div>
            </DialogContent>
          </Dialog>

          {/* View All Permissions Dialog */}
          <Dialog open={showPermissionsDialog} onOpenChange={setShowPermissionsDialog}>
            <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  All Permissions - {selectedRole?.displayName}
                </DialogTitle>
                <DialogDescription>
                  View all permissions including inherited permissions from parent roles.
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-6">
                {/* Permission Summary */}
                <div className="grid grid-cols-3 gap-4 p-4 bg-muted/50 rounded-lg">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {allPermissions.filter(p => p.source === 'direct').length}
                    </div>
                    <div className="text-sm text-muted-foreground">Direct Permissions</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {allPermissions.filter(p => p.source === 'inherited').length}
                    </div>
                    <div className="text-sm text-muted-foreground">Inherited Permissions</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">
                      {allPermissions.length}
                    </div>
                    <div className="text-sm text-muted-foreground">Total Permissions</div>
                  </div>
                </div>

                {/* Inheritance Chain */}
                {selectedRole?.parentRole && (
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Inheritance Chain</h4>
                    <div className="flex items-center gap-2 text-sm">
                      <Badge variant="outline">{selectedRole.parentRole.displayName}</Badge>
                      <ArrowRight className="h-4 w-4 text-muted-foreground" />
                      <Badge variant="default">{selectedRole.displayName}</Badge>
                    </div>
                  </div>
                )}

                {/* Permissions by Category */}
                <div className="space-y-4">
                  <h4 className="text-sm font-medium">Permissions by Category</h4>
                  {Object.entries(
                    allPermissions.reduce((acc, perm) => {
                      const category = perm.category;
                      if (!acc[category]) {
                        acc[category] = [];
                      }
                      acc[category].push(perm);
                      return acc;
                    }, {} as Record<string, PermissionWithSource[]>)
                  ).map(([category, perms]) => (
                    <div key={category} className="space-y-2">
                      <div className="flex items-center gap-2">
                        <h5 className="text-sm font-medium capitalize">{category}</h5>
                        <Badge variant="outline" className="text-xs">
                          {perms.length} permissions
                        </Badge>
                      </div>
                      <div className="grid grid-cols-1 gap-2">
                        {perms.map((perm) => (
                          <div
                            key={perm.name}
                            className={`p-3 rounded-lg border ${
                              perm.source === 'direct'
                                ? 'bg-blue-50 border-blue-200'
                                : 'bg-green-50 border-green-200'
                            }`}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex-1">
                                <div className="flex items-center gap-2">
                                  <span className="font-medium text-sm">{perm.displayName}</span>
                                  <Badge
                                    variant={perm.source === 'direct' ? 'default' : 'secondary'}
                                    className="text-xs"
                                  >
                                    {perm.source === 'direct' ? 'Direct' : 'Inherited'}
                                  </Badge>
                                </div>
                                <p className="text-xs text-muted-foreground mt-1">
                                  {perm.description}
                                </p>
                                {perm.source === 'inherited' && perm.inheritedFrom && (
                                  <p className="text-xs text-green-600 mt-1">
                                    Inherited from: {perm.inheritedFrom}
                                  </p>
                                )}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex justify-end">
                <Button
                  variant="outline"
                  onClick={() => setShowPermissionsDialog(false)}
                >
                  Close
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </>
      )}
    </div>
  );
}
