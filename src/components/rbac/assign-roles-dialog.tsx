"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Shield, Users, Key, Trash2 } from "lucide-react";
import { toast } from "sonner";

interface Role {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  isSystemRole: boolean;
  permissions: Array<{
    name: string;
    displayName: string;
    category: string;
  }>;
  userCount: number;
}

interface UserRole {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  isSystemRole: boolean;
  assignedAt: string;
  expiresAt?: string;
}

interface User {
  id: string;
  name: string;
  email: string;
  customRoles: UserRole[];
}

interface AssignRolesDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  user: User;
  onSuccess: () => void;
}

export function AssignRolesDialog({ open, onOpenChange, user, onSuccess }: AssignRolesDialogProps) {
  const [availableRoles, setAvailableRoles] = useState<Role[]>([]);
  const [selectedRoleIds, setSelectedRoleIds] = useState<string[]>([]);
  const [rolesToRevoke, setRolesToRevoke] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const fetchRoles = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/rbac/roles");
      if (!response.ok) {
        throw new Error("Failed to fetch roles");
      }
      const data = await response.json();
      setAvailableRoles(data.data);
    } catch (error) {
      console.error("Error fetching roles:", error);
      toast.error("Failed to fetch roles");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (open) {
      fetchRoles();
      setSelectedRoleIds([]);
      setRolesToRevoke([]);
    }
  }, [open]);

  const handleAssignRoles = async () => {
    if (selectedRoleIds.length === 0) {
      toast.error("Please select at least one role to assign");
      return;
    }

    try {
      setIsSubmitting(true);
      const response = await fetch(`/api/rbac/users/${user.id}/roles`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          roleIds: selectedRoleIds,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to assign roles");
      }

      toast.success("Roles assigned successfully");
      setSelectedRoleIds([]);
      onSuccess();
    } catch (error) {
      console.error("Error assigning roles:", error);
      toast.error(error instanceof Error ? error.message : "Failed to assign roles");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRevokeRoles = async () => {
    if (rolesToRevoke.length === 0) {
      toast.error("Please select at least one role to revoke");
      return;
    }

    try {
      setIsSubmitting(true);
      const response = await fetch(`/api/rbac/users/${user.id}/roles`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          roleIds: rolesToRevoke,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to revoke roles");
      }

      toast.success("Roles revoked successfully");
      setRolesToRevoke([]);
      onSuccess();
    } catch (error) {
      console.error("Error revoking roles:", error);
      toast.error(error instanceof Error ? error.message : "Failed to revoke roles");
    } finally {
      setIsSubmitting(false);
    }
  };

  const currentRoleIds = user.customRoles.map(role => role.id);
  const availableToAssign = availableRoles.filter(role => !currentRoleIds.includes(role.id));

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Manage Roles for {user.name}
          </DialogTitle>
          <DialogDescription>
            Assign or revoke roles for this user
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Current Roles */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Current Roles
              </CardTitle>
              <CardDescription>
                Roles currently assigned to this user
              </CardDescription>
            </CardHeader>
            <CardContent>
              {user.customRoles.length === 0 ? (
                <p className="text-muted-foreground text-center py-4">
                  No custom roles assigned
                </p>
              ) : (
                <div className="space-y-3">
                  {user.customRoles.map((role) => (
                    <div key={role.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <Checkbox
                          checked={rolesToRevoke.includes(role.id)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setRolesToRevoke([...rolesToRevoke, role.id]);
                            } else {
                              setRolesToRevoke(rolesToRevoke.filter(id => id !== role.id));
                            }
                          }}
                        />
                        <div>
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{role.displayName}</span>
                            {role.isSystemRole && (
                              <Badge variant="secondary" className="text-xs">
                                System
                              </Badge>
                            )}
                          </div>
                          {role.description && (
                            <p className="text-sm text-muted-foreground">{role.description}</p>
                          )}
                          <p className="text-xs text-muted-foreground">
                            Assigned on {new Date(role.assignedAt).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                  
                  {rolesToRevoke.length > 0 && (
                    <div className="flex justify-end pt-2">
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={handleRevokeRoles}
                        disabled={isSubmitting}
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Revoke Selected ({rolesToRevoke.length})
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          <Separator />

          {/* Available Roles */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Key className="h-5 w-5" />
                Available Roles
              </CardTitle>
              <CardDescription>
                Select roles to assign to this user
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-4">Loading available roles...</div>
              ) : availableToAssign.length === 0 ? (
                <p className="text-muted-foreground text-center py-4">
                  All available roles are already assigned to this user
                </p>
              ) : (
                <div className="space-y-3">
                  {availableToAssign.map((role) => (
                    <div key={role.id} className="flex items-center space-x-3 p-3 border rounded-lg">
                      <Checkbox
                        checked={selectedRoleIds.includes(role.id)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedRoleIds([...selectedRoleIds, role.id]);
                          } else {
                            setSelectedRoleIds(selectedRoleIds.filter(id => id !== role.id));
                          }
                        }}
                      />
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{role.displayName}</span>
                          {role.isSystemRole && (
                            <Badge variant="secondary" className="text-xs">
                              System
                            </Badge>
                          )}
                        </div>
                        {role.description && (
                          <p className="text-sm text-muted-foreground">{role.description}</p>
                        )}
                        <div className="flex items-center gap-4 text-xs text-muted-foreground mt-1">
                          <span>{role.permissions.length} permissions</span>
                          <span>{role.userCount} users</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          {selectedRoleIds.length > 0 && (
            <Button
              onClick={handleAssignRoles}
              disabled={isSubmitting}
            >
              {isSubmitting ? "Assigning..." : `Assign Selected (${selectedRoleIds.length})`}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
