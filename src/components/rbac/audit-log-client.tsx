"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  Activity, 
  Search, 
  Filter,
  Calendar,
  Users,
  Shield,
  Key,
  CheckCircle,
  XCircle,
  Clock,
} from "lucide-react";
import { toast } from "sonner";

interface User {
  id: string;
  name: string;
  email: string;
}

interface Permission {
  name: string;
  displayName: string;
  category: string;
}

interface Role {
  id: string;
  name: string;
  displayName: string;
}

interface AuditEntry {
  id: string;
  type: "permission_usage" | "role_assignment";
  action: string;
  user: User;
  permission?: Permission;
  role?: Role;
  resourceType?: string;
  resourceId?: string;
  granted?: boolean;
  ipAddress?: string;
  userAgent?: string;
  metadata?: any;
  expiresAt?: string;
  timestamp: string;
}

interface AuditStats {
  totalPermissionChecks: number;
  totalRoleAssignments: number;
  activeUsers: number;
}

interface AuditData {
  auditEntries: AuditEntry[];
  stats: AuditStats;
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export function AuditLogClient() {
  const [auditData, setAuditData] = useState<AuditData>({
    auditEntries: [],
    stats: {
      totalPermissionChecks: 0,
      totalRoleAssignments: 0,
      activeUsers: 0,
    },
    pagination: {
      page: 1,
      limit: 50,
      total: 0,
      pages: 0,
    },
  });
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [actionFilter, setActionFilter] = useState("");
  const [dateRange, setDateRange] = useState({
    startDate: "",
    endDate: "",
  });

  const fetchAuditLog = async (page = 1) => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: auditData.pagination.limit.toString(),
        ...(searchTerm && { userId: searchTerm }),
        ...(actionFilter && { action: actionFilter }),
        ...(dateRange.startDate && { startDate: dateRange.startDate }),
        ...(dateRange.endDate && { endDate: dateRange.endDate }),
      });

      const response = await fetch(`/api/rbac/audit?${params}`);
      if (!response.ok) {
        throw new Error("Failed to fetch audit log");
      }
      const data = await response.json();
      setAuditData(data.data);
    } catch (error) {
      console.error("Error fetching audit log:", error);
      toast.error("Failed to fetch audit log");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAuditLog();
  }, []);

  const handleSearch = () => {
    fetchAuditLog(1);
  };

  const getActionIcon = (entry: AuditEntry) => {
    if (entry.type === "permission_usage") {
      return entry.granted ? (
        <CheckCircle className="h-4 w-4 text-green-500" />
      ) : (
        <XCircle className="h-4 w-4 text-red-500" />
      );
    }
    return <Shield className="h-4 w-4 text-blue-500" />;
  };

  const getActionBadge = (entry: AuditEntry) => {
    if (entry.type === "permission_usage") {
      return (
        <Badge variant={entry.granted ? "default" : "destructive"} className="text-xs">
          {entry.granted ? "Granted" : "Denied"}
        </Badge>
      );
    }
    return (
      <Badge variant="secondary" className="text-xs">
        Role Assignment
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Audit Log</h2>
          <p className="text-muted-foreground">
            Track access and permission changes across your organization
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Permission Checks</CardTitle>
            <Key className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{auditData.stats.totalPermissionChecks}</div>
            <p className="text-xs text-muted-foreground">
              Last 30 days
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Role Assignments</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{auditData.stats.totalRoleAssignments}</div>
            <p className="text-xs text-muted-foreground">
              Total active assignments
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{auditData.stats.activeUsers}</div>
            <p className="text-xs text-muted-foreground">
              Last 30 days
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Search & Filter
          </CardTitle>
          <CardDescription>
            Filter audit entries by user, action, or date range
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by user ID..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Input
              placeholder="Filter by action..."
              value={actionFilter}
              onChange={(e) => setActionFilter(e.target.value)}
            />
            <Input
              type="date"
              placeholder="Start date"
              value={dateRange.startDate}
              onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}
            />
            <Input
              type="date"
              placeholder="End date"
              value={dateRange.endDate}
              onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
            />
          </div>
          <div className="flex justify-end mt-4">
            <Button onClick={handleSearch}>
              <Filter className="h-4 w-4 mr-2" />
              Apply Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Audit Log Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Audit Entries
          </CardTitle>
          <CardDescription>
            Recent access and permission changes
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>User</TableHead>
                <TableHead>Action</TableHead>
                <TableHead>Details</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Timestamp</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-8">
                    Loading audit entries...
                  </TableCell>
                </TableRow>
              ) : auditData.auditEntries.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-8">
                    No audit entries found
                  </TableCell>
                </TableRow>
              ) : (
                auditData.auditEntries.map((entry) => (
                  <TableRow key={entry.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback>
                            {getInitials(entry.user.name)}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{entry.user.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {entry.user.email}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getActionIcon(entry)}
                        <span className="font-medium">{entry.action}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        {entry.permission && (
                          <div className="text-sm">
                            <span className="font-medium">Permission:</span> {entry.permission.displayName}
                          </div>
                        )}
                        {entry.role && (
                          <div className="text-sm">
                            <span className="font-medium">Role:</span> {entry.role.displayName}
                          </div>
                        )}
                        {entry.resourceType && (
                          <div className="text-sm text-muted-foreground">
                            Resource: {entry.resourceType}
                            {entry.resourceId && ` (${entry.resourceId})`}
                          </div>
                        )}
                        {entry.ipAddress && (
                          <div className="text-xs text-muted-foreground">
                            IP: {entry.ipAddress}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      {getActionBadge(entry)}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2 text-sm">
                        <Clock className="h-3 w-3" />
                        {formatDate(entry.timestamp)}
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Pagination */}
      {auditData.pagination.pages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            Showing {(auditData.pagination.page - 1) * auditData.pagination.limit + 1} to{" "}
            {Math.min(auditData.pagination.page * auditData.pagination.limit, auditData.pagination.total)} of{" "}
            {auditData.pagination.total} entries
          </p>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => fetchAuditLog(auditData.pagination.page - 1)}
              disabled={auditData.pagination.page <= 1}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => fetchAuditLog(auditData.pagination.page + 1)}
              disabled={auditData.pagination.page >= auditData.pagination.pages}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
