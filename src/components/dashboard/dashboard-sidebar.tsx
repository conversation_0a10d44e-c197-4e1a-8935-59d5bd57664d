"use client";

import * as React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useSession } from "next-auth/react";
import { cn } from "@/lib/utils";
import { navigationConfig } from "@/config/navigation";
import { ScrollArea } from "@/components/ui/scroll-area";

export function DashboardSidebar() {
  const pathname = usePathname();
  const { data: session } = useSession();
  const [isCollapsed, setIsCollapsed] = React.useState(false);
  const [isHovering, setIsHovering] = React.useState(false);

  // Filter navigation items based on user role
  const filteredNavigation = React.useMemo(() => {
    return navigationConfig.mainNav.map(section => ({
      ...section,
      items: section.items?.filter(item => {
        // If no roles specified, show to everyone
        if (!item.roles || item.roles.length === 0) {
          return true;
        }
        // Check if user has required role
        return item.roles.includes(session?.user?.role as string);
      }) || []
    })).filter(section => section.items.length > 0); // Remove empty sections
  }, [session?.user?.role]);

  const handleMouseEnter = () => {
    if (isCollapsed) {
      setIsHovering(true);
    }
  };

  const handleMouseLeave = () => {
    if (isCollapsed) {
      setIsHovering(false);
    }
  };

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
    if (!isCollapsed) {
      setIsHovering(false);
    }
  };

  return (
    <aside
      className={cn(
        "group relative flex flex-col border-r bg-background transition-all duration-300",
        isCollapsed && !isHovering ? "w-[60px]" : "w-[240px]"
      )}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className="flex h-10 items-center border-b px-2">
        <button
          onClick={toggleCollapse}
          className="inline-flex h-5 w-5 items-center justify-center rounded hover:bg-muted"
        >
          {isCollapsed ? (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="transition-transform duration-200"
            >
              <path d="m9 18 6-6-6-6" />
            </svg>
          ) : (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="transition-transform duration-200"
            >
              <path d="m15 18-6-6 6-6" />
            </svg>
          )}
          <span className="sr-only">Toggle Sidebar</span>
        </button>
        {(!isCollapsed || isHovering) && (
          <span className="ml-1.5 text-sm font-semibold">Navigation</span>
        )}
      </div>
      <ScrollArea className="flex-1 py-1">
        <nav className="grid gap-1 px-1">
          {filteredNavigation.map((section) => (
            <div key={section.title} className="py-1">
              {(!isCollapsed || isHovering) && (
                <h4 className="mb-0.5 rounded px-1.5 py-0.5 text-xs font-semibold">
                  {section.title}
                </h4>
              )}
              {section.items?.map((item) => {
                // Check if current path matches or starts with the navigation item's path
                const isActive = pathname === item.href ||
                  (item.href !== '/dashboard' && pathname.startsWith(item.href + '/'));

                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={cn(
                      "flex items-center gap-2 rounded px-2 py-1.5 text-xs font-medium hover:bg-muted",
                      isActive
                        ? "bg-muted"
                        : "transparent",
                      isCollapsed && !isHovering ? "justify-center" : ""
                    )}
                  >
                    {item.icon && (
                      <item.icon className={cn("h-3 w-3", isActive ? "text-primary" : "text-muted-foreground")} />
                    )}
                    {(!isCollapsed || isHovering) && (
                      <span className="text-xs">{item.title}</span>
                    )}
                  </Link>
                );
              })}
            </div>
          ))}
        </nav>
      </ScrollArea>
    </aside>
  );
}
