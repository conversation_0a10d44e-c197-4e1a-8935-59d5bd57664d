"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import {
  Upload,
  FileText,
  Clock,
  CheckCircle,
  AlertCircle,
  TrendingUp,
  FolderOpen,
  Calendar,
  Target,
} from "lucide-react";
import Link from "next/link";
import { SPVWelcomeGuide } from "@/components/spv/spv-welcome-guide";

interface SiteWorkerStats {
  assignedProjects: number;
  totalUnitLogs: number;
  userUnitLogs: number;
  draftEntries: number;
  submittedEntries: number;
  approvedEntries: number;
  rejectedEntries: number;
  verificationRate: number;
  rejectionRate: number;
}

interface RecentActivity {
  id: string;
  type: string;
  description: string;
  timestamp: string;
  projectName: string;
  projectId: string;
  unitLogId?: string;
}

interface Project {
  id: string;
  name: string;
  type: string;
  status: string;
  statistics: {
    userUnitLogs: number;
    draftCount: number;
    completionRate: number;
  };
}

interface SiteWorkerDashboardProps {
  spvUser: any;
}

export function SiteWorkerDashboard({ spvUser }: SiteWorkerDashboardProps) {
  const [stats, setStats] = useState<SiteWorkerStats | null>(null);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showWelcomeGuide, setShowWelcomeGuide] = useState(true);

  useEffect(() => {
    if (spvUser) {
      fetchDashboardData();
    }
  }, [spvUser]);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      const [statsResponse, activityResponse, projectsResponse] = await Promise.all([
        fetch("/api/spv/dashboard/stats"),
        fetch("/api/spv/dashboard/activity"),
        fetch("/api/spv/projects"),
      ]);

      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData.data);
      }

      if (activityResponse.ok) {
        const activityData = await activityResponse.json();
        setRecentActivity(activityData.data || []);
      }

      if (projectsResponse.ok) {
        const projectsData = await projectsResponse.json();
        setProjects(projectsData.data.projects || []);
      }

    } catch (err) {
      setError("Failed to load dashboard data");
      console.error("Dashboard data fetch error:", err);
    } finally {
      setLoading(false);
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'DATA_ENTRY':
        return <Upload className="h-4 w-4 text-blue-500" />;
      case 'VERIFICATION':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'REJECTION':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-4 w-4" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16 mb-2" />
                <Skeleton className="h-3 w-24" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-8">
      {/* Welcome Guide for New Users */}
      {showWelcomeGuide && (
        <SPVWelcomeGuide
          spvUser={spvUser}
          onDismiss={() => setShowWelcomeGuide(false)}
        />
      )}

      {/* Welcome Section */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Welcome back, {spvUser?.user?.name}</h1>
            <p className="text-muted-foreground">
              Site Worker Dashboard • {spvUser?.spv?.name}
            </p>
          </div>
          <Button asChild>
            <Link href="/spv/data-entry">
              <Upload className="h-4 w-4 mr-2" />
              New Data Entry
            </Link>
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-normal">Assigned Projects</CardTitle>
            <FolderOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-xl font-bold">{stats?.assignedProjects || 0}</div>
            <p className="text-xs font-normal text-muted-foreground">
              Active project assignments
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-normal">Your Entries</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-xl font-bold">{stats?.userUnitLogs || 0}</div>
            <p className="text-xs font-normal text-muted-foreground">
              Total data entries created
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-normal">Draft Entries</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-xl font-bold">{stats?.draftEntries || 0}</div>
            <p className="text-xs font-normal text-muted-foreground">
              Entries ready to submit
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-normal">Approval Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-xl font-bold">{stats?.verificationRate || 0}%</div>
            <p className="text-xs font-normal text-muted-foreground">
              Entries successfully verified
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks for data entry and management
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button className="w-full justify-start" asChild>
              <Link href="/spv/data-entry">
                <Upload className="h-4 w-4 mr-2" />
                Create New Data Entry
              </Link>
            </Button>
            <Button variant="outline" className="w-full justify-start" asChild>
              <Link href="/spv/data-entry?view=list">
                <FileText className="h-4 w-4 mr-2" />
                View My Entries
              </Link>
            </Button>
            <Button variant="outline" className="w-full justify-start" asChild>
              <Link href="/spv/projects">
                <FolderOpen className="h-4 w-4 mr-2" />
                Browse Projects
              </Link>
            </Button>
            <Button variant="outline" className="w-full justify-start" asChild>
              <Link href="/spv/verification">
                <CheckCircle className="h-4 w-4 mr-2" />
                Check Verification Status
              </Link>
            </Button>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>
              Your latest data entry and verification activities
            </CardDescription>
          </CardHeader>
          <CardContent>
            {recentActivity.length > 0 ? (
              <div className="space-y-3">
                {recentActivity.slice(0, 5).map((activity) => (
                  <div key={activity.id} className="flex items-start gap-3">
                    {getActivityIcon(activity.type)}
                    <div className="flex-1 space-y-1">
                      <p className="text-sm font-medium">{activity.description}</p>
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <span>{activity.projectName}</span>
                        <span>•</span>
                        <span>{new Date(activity.timestamp).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-6">
                <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No recent activity</h3>
                <p className="text-muted-foreground mb-4">
                  Start by creating your first data entry.
                </p>
                <Button asChild>
                  <Link href="/spv/data-entry">
                    <Upload className="h-4 w-4 mr-2" />
                    Create Entry
                  </Link>
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Project Progress */}
      <Card>
        <CardHeader>
          <CardTitle>Project Progress</CardTitle>
          <CardDescription>
            Your data entry progress across assigned projects
          </CardDescription>
        </CardHeader>
        <CardContent>
          {projects.length > 0 ? (
            <div className="space-y-4">
              {projects.slice(0, 3).map((project) => (
                <div key={project.id} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">{project.name}</h4>
                      <p className="text-sm text-muted-foreground">{project.type}</p>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">{project.statistics.userUnitLogs} entries</div>
                      <div className="text-xs text-muted-foreground">
                        {project.statistics.draftCount} drafts
                      </div>
                    </div>
                  </div>
                  <Progress value={project.statistics.completionRate} className="h-2" />
                </div>
              ))}
              {projects.length > 3 && (
                <Button variant="outline" className="w-full" asChild>
                  <Link href="/spv/projects">
                    View All Projects ({projects.length})
                  </Link>
                </Button>
              )}
            </div>
          ) : (
            <div className="text-center py-6">
              <Target className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No projects assigned</h3>
              <p className="text-muted-foreground">
                Contact your SPV administrator to get project assignments.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
