"use client";

import { useState } from "react";
import { Check, ChevronRight, Building2, FileText, Shield, Upload } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { SPVCreationForm } from "./spv-creation-form";
import { SPVVerificationForm } from "./spv-verification-form";
import { SPVDocumentUpload } from "./spv-document-upload";
import { SPVCreateData, SPVVerificationData, SPV } from "@/types/spv";
import { toast } from "sonner";

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  status: 'pending' | 'current' | 'completed';
  optional?: boolean;
}

interface SPVOnboardingStepperProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: (spv: SPV) => void;
  mode?: "user" | "admin";
  organizations?: Array<{
    id: string;
    name: string;
    legalName?: string | null;
  }>;
}

export function SPVOnboardingStepper({
  isOpen,
  onClose,
  onComplete,
  mode = "user",
  organizations = [],
}: SPVOnboardingStepperProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [createdSPV, setCreatedSPV] = useState<SPV | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const steps: OnboardingStep[] = [
    {
      id: "basic-info",
      title: "Basic Information",
      description: "Create your SPV with basic details",
      icon: Building2,
      status: currentStep === 0 ? 'current' : currentStep > 0 ? 'completed' : 'pending',
    },
    {
      id: "verification-details",
      title: "Verification Details",
      description: "Complete verification information",
      icon: Shield,
      status: currentStep === 1 ? 'current' : currentStep > 1 ? 'completed' : 'pending',
      optional: true,
    },
    {
      id: "document-upload",
      title: "Document Upload",
      description: "Upload required verification documents",
      icon: Upload,
      status: currentStep === 2 ? 'current' : currentStep > 2 ? 'completed' : 'pending',
      optional: true,
    },
    {
      id: "review-submit",
      title: "Review & Submit",
      description: "Review and submit for verification",
      icon: FileText,
      status: currentStep === 3 ? 'current' : currentStep > 3 ? 'completed' : 'pending',
    },
  ];

  const handleSPVCreation = async (data: SPVCreateData) => {
    setIsSubmitting(true);
    try {
      const response = await fetch('/api/organizations/spvs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Failed to create SPV');
      }

      const result = await response.json();
      setCreatedSPV(result.spv);
      toast.success("SPV created successfully!");
      setCurrentStep(1);
    } catch (error) {
      console.error('Error creating SPV:', error);
      toast.error("Failed to create SPV. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleVerificationDetails = async (data: SPVVerificationData) => {
    if (!createdSPV) return;

    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/organizations/spvs/${createdSPV.id}/verification`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Failed to update verification details');
      }

      const result = await response.json();
      setCreatedSPV(result.spv);
      toast.success("Verification details updated!");
      setCurrentStep(2);
    } catch (error) {
      console.error('Error updating verification details:', error);
      toast.error("Failed to update verification details. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDocumentUpload = (document: any) => {
    toast.success(`Document "${document.fileName}" uploaded successfully!`);
  };

  const handleSkipStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleComplete = () => {
    if (createdSPV) {
      onComplete(createdSPV);
      handleClose();
    }
  };

  const handleClose = () => {
    setCurrentStep(0);
    setCreatedSPV(null);
    setIsSubmitting(false);
    onClose();
  };

  const getStepProgress = () => {
    return ((currentStep + 1) / steps.length) * 100;
  };

  const canSkipCurrentStep = () => {
    return steps[currentStep]?.optional && createdSPV;
  };

  const canCompleteOnboarding = () => {
    return createdSPV && currentStep >= steps.length - 1;
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            SPV Onboarding
          </DialogTitle>
          <DialogDescription>
            Complete the SPV onboarding process step by step
          </DialogDescription>
        </DialogHeader>

        {/* Progress Header */}
        <div className="space-y-4">
          <div className="flex items-center justify-between text-sm">
            <span className="font-medium">Step {currentStep + 1} of {steps.length}</span>
            <span className="text-muted-foreground">{Math.round(getStepProgress())}% Complete</span>
          </div>
          <Progress value={getStepProgress()} className="h-2" />
        </div>

        {/* Steps Navigation */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 py-4">
          {steps.map((step, index) => {
            const Icon = step.icon;
            return (
              <div
                key={step.id}
                className={`flex items-center gap-3 p-3 rounded-lg border transition-colors ${
                  step.status === 'current' 
                    ? 'border-primary bg-primary/5' 
                    : step.status === 'completed'
                    ? 'border-green-200 bg-green-50'
                    : 'border-border bg-muted/30'
                }`}
              >
                <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                  step.status === 'completed' 
                    ? 'bg-green-600 text-white'
                    : step.status === 'current'
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-muted text-muted-foreground'
                }`}>
                  {step.status === 'completed' ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    <Icon className="h-4 w-4" />
                  )}
                </div>
                <div className="min-w-0 flex-1">
                  <p className={`text-sm font-medium truncate ${
                    step.status === 'current' ? 'text-primary' : 
                    step.status === 'completed' ? 'text-green-700' : 
                    'text-muted-foreground'
                  }`}>
                    {step.title}
                    {step.optional && (
                      <Badge variant="outline" className="ml-1 text-xs">Optional</Badge>
                    )}
                  </p>
                  <p className="text-xs text-muted-foreground truncate">{step.description}</p>
                </div>
              </div>
            );
          })}
        </div>

        {/* Step Content */}
        <div className="space-y-6">
          {/* Step 1: Basic Information */}
          {currentStep === 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="h-5 w-5" />
                  Create SPV
                </CardTitle>
                <CardDescription>
                  Enter the basic information for your Special Purpose Vehicle
                </CardDescription>
              </CardHeader>
              <CardContent>
                <SPVCreationForm
                  isOpen={true}
                  onClose={() => {}}
                  onSubmit={handleSPVCreation}
                  mode={mode}
                  organizations={organizations}
                  isSubmitting={isSubmitting}
                />
              </CardContent>
            </Card>
          )}

          {/* Step 2: Verification Details */}
          {currentStep === 1 && createdSPV && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Verification Details
                </CardTitle>
                <CardDescription>
                  Complete the verification information for your SPV
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Alert className="mb-4">
                  <Shield className="h-4 w-4" />
                  <AlertDescription>
                    This step is optional. You can complete verification details later from your SPV dashboard.
                  </AlertDescription>
                </Alert>
                <SPVVerificationForm
                  isOpen={true}
                  onClose={() => {}}
                  onSubmit={handleVerificationDetails}
                  initialData={{
                    name: createdSPV.name,
                    purpose: createdSPV.purpose || "",
                    legalStructure: createdSPV.legalStructure || "",
                    registrationNumber: createdSPV.registrationNumber || "",
                    jurisdiction: createdSPV.jurisdiction || "",
                    country: createdSPV.country || "",
                  }}
                  spvId={createdSPV.id}
                  isSubmitting={isSubmitting}
                />
              </CardContent>
            </Card>
          )}

          {/* Step 3: Document Upload */}
          {currentStep === 2 && createdSPV && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Upload className="h-5 w-5" />
                  Upload Documents
                </CardTitle>
                <CardDescription>
                  Upload the required verification documents
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Alert className="mb-4">
                  <Upload className="h-4 w-4" />
                  <AlertDescription>
                    This step is optional. You can upload documents later from your SPV dashboard.
                  </AlertDescription>
                </Alert>
                <SPVDocumentUpload
                  spvId={createdSPV.id}
                  onUploadComplete={handleDocumentUpload}
                />
              </CardContent>
            </Card>
          )}

          {/* Step 4: Review & Submit */}
          {currentStep === 3 && createdSPV && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Review & Complete
                </CardTitle>
                <CardDescription>
                  Review your SPV information and complete the onboarding
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Check className="h-5 w-5 text-green-600" />
                    <h3 className="font-medium text-green-800">SPV Created Successfully!</h3>
                  </div>
                  <p className="text-sm text-green-700">
                    Your SPV "{createdSPV.name}" has been created and is ready for use.
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium mb-2">SPV Information</h4>
                    <div className="space-y-1 text-sm">
                      <p><span className="font-medium">Name:</span> {createdSPV.name}</p>
                      <p><span className="font-medium">Status:</span> {createdSPV.status}</p>
                      <p><span className="font-medium">Verification:</span> {createdSPV.verificationStatus?.replace(/_/g, ' ')}</p>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">Next Steps</h4>
                    <ul className="space-y-1 text-sm text-muted-foreground">
                      <li>• Complete verification details if not done</li>
                      <li>• Upload required documents</li>
                      <li>• Assign projects to your SPV</li>
                      <li>• Create SPV users and manage permissions</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between pt-6 border-t border-border">
          <Button variant="outline" onClick={handleClose} disabled={isSubmitting}>
            {currentStep === 0 ? 'Cancel' : 'Close'}
          </Button>
          
          <div className="flex gap-3">
            {canSkipCurrentStep() && (
              <Button variant="outline" onClick={handleSkipStep} disabled={isSubmitting}>
                Skip Step
              </Button>
            )}
            
            {currentStep < steps.length - 1 && !canSkipCurrentStep() && (
              <Button onClick={() => setCurrentStep(currentStep + 1)} disabled={!createdSPV || isSubmitting}>
                Next Step
                <ChevronRight className="ml-2 h-4 w-4" />
              </Button>
            )}
            
            {canCompleteOnboarding() && (
              <Button onClick={handleComplete} disabled={isSubmitting}>
                Complete Onboarding
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
