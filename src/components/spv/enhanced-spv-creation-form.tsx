"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Loader2, Building2, Info, ChevronLeft, ChevronRight, Shield, CreditCard, FileText, Calendar } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { spvVerificationSchema } from "@/lib/validation/schemas";
import { SPVVerificationData } from "@/types/spv";
import { toast } from "sonner";

// Enhanced SPV creation schema with all fields optional except name
const enhancedSPVCreationSchema = z.object({
  // Basic Information - only name is required
  name: z.string().min(1, "SPV name is required"),
  purpose: z.string().optional(),
  legalStructure: z.string().optional(),
  projectCategories: z.string().optional(),
  adminName: z.string().optional(),
  adminEmail: z.string().optional().or(z.literal("")), // Allow empty string

  // Legal & Compliance (all optional)
  jurisdiction: z.string().optional(),
  country: z.string().optional(),
  gstNumber: z.string().optional(),
  cinNumber: z.string().optional(),
  panNumber: z.string().optional(),
  incorporationDate: z.string().optional(),
  registeredAddress: z.string().optional(),
  description: z.string().optional(),

  // Admin mode
  organizationId: z.string().optional(),
});

type SPVFormValues = z.infer<typeof enhancedSPVCreationSchema>;

const legalStructures = [
  { value: "Private Limited Company", label: "Private Limited Company" },
  { value: "Limited Liability Partnership", label: "Limited Liability Partnership (LLP)" },
  { value: "Partnership", label: "Partnership" },
  { value: "Sole Proprietorship", label: "Sole Proprietorship" },
  { value: "Trust", label: "Trust" },
  { value: "Society", label: "Society" },
  { value: "Other", label: "Other" },
];

const projectCategories = [
  { value: "Solar", label: "Solar Energy" },
  { value: "Wind", label: "Wind Energy" },
  { value: "Hybrid", label: "Hybrid Renewable" },
  { value: "Forestry", label: "Forestry & REDD+" },
  { value: "Energy Efficiency", label: "Energy Efficiency" },
  { value: "Waste Management", label: "Waste Management" },
  { value: "Transportation", label: "Transportation" },
  { value: "Other", label: "Other" },
];

// Field descriptions for info tooltips
const fieldDescriptions = {
  name: "The official name of your Special Purpose Vehicle",
  purpose: "Brief description of the SPV's business purpose and objectives",
  legalStructure: "The legal form of your organization (e.g., Private Limited Company, LLP)",
  jurisdiction: "The state or territory where the SPV is registered",
  country: "Country of incorporation and operation",
  gstNumber: "Goods and Services Tax identification number (15 digits)",
  cinNumber: "Corporate Identity Number assigned by MCA (21 characters)",
  panNumber: "Permanent Account Number for tax purposes (10 characters)",
  incorporationDate: "Date when the company was officially incorporated",
  registeredAddress: "Official registered address as per incorporation documents",
  projectCategories: "Type of carbon credit projects this SPV will manage",
  adminName: "Name of the SPV administrator",
  adminEmail: "Email address for the SPV administrator account"
};

// Info button component
const InfoButton = ({ description }: { description: string }) => (
  <TooltipProvider>
    <Tooltip>
      <TooltipTrigger asChild>
        <Info className="h-3 w-3 text-muted-foreground cursor-help ml-1 bg-white rounded-full p-0.5 border border-gray-200" />
      </TooltipTrigger>
      <TooltipContent className="max-w-xs !bg-white !border !border-gray-200 !text-gray-900 shadow-lg">
        <p className="text-sm">{description}</p>
      </TooltipContent>
    </Tooltip>
  </TooltipProvider>
);

// Form steps configuration
const formSteps = [
  {
    id: "basic",
    title: "Basic Information",
    icon: Building2,
  },
  {
    id: "legal",
    title: "Legal & Compliance",
    icon: Shield,
  },
];

interface Organization {
  id: string;
  name: string;
  legalName?: string | null;
}

interface EnhancedSPVCreationFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: SPVVerificationData | (SPVVerificationData & { organizationId: string })) => Promise<void>;
  organizations?: Organization[];
  mode: "user" | "admin";
  isSubmitting?: boolean;
  initialData?: Partial<SPVFormValues>;
}

export function EnhancedSPVCreationForm({
  isOpen,
  onClose,
  onSubmit,
  organizations = [],
  mode = "user",
  isSubmitting = false,
  initialData,
}: EnhancedSPVCreationFormProps) {
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [isFormSubmitting, setIsFormSubmitting] = useState(false);

  const isAdminMode = mode === "admin";

  const form = useForm<SPVFormValues>({
    resolver: zodResolver(enhancedSPVCreationSchema),
    defaultValues: {
      name: initialData?.name || "",
      purpose: initialData?.purpose || "",
      legalStructure: initialData?.legalStructure || "",
      jurisdiction: initialData?.jurisdiction || "",
      country: initialData?.country || "",
      gstNumber: initialData?.gstNumber || "",
      cinNumber: initialData?.cinNumber || "",
      panNumber: initialData?.panNumber || "",
      incorporationDate: initialData?.incorporationDate || "",
      registeredAddress: initialData?.registeredAddress || "",
      projectCategories: initialData?.projectCategories || "",
      description: initialData?.description || "",
      adminName: initialData?.adminName || "",
      adminEmail: initialData?.adminEmail || "",
      ...(isAdminMode && { organizationId: "" }),
    },
  });

  const handleSubmit = async (values: SPVFormValues) => {
    try {
      setSubmitError(null);
      setIsFormSubmitting(true);

      // Transform data to match API expectations
      const submitData = {
        ...values,
        // Keep projectCategories as string (backend will handle it)
        projectCategories: values.projectCategories,
      };

      await onSubmit(submitData as any);

      // Reset form and close dialog on success
      form.reset();
      setCurrentStep(0);
      onClose();
    } catch (error) {
      console.error("SPV creation error:", error);
      setSubmitError(error instanceof Error ? error.message : "Failed to create SPV");
    } finally {
      setIsFormSubmitting(false);
    }
  };

  const handleNext = async () => {
    const currentStepFields = getCurrentStepFields();

    // If there are fields to validate, validate them
    if (currentStepFields.length > 0) {
      const isValid = await form.trigger(currentStepFields);
      if (!isValid) {
        return; // Don't advance if validation fails
      }
    }

    // Always advance to next step if we're not at the last step
    if (currentStep < formSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleCancel = () => {
    form.reset();
    setCurrentStep(0);
    setSubmitError(null);
    setIsFormSubmitting(false);
    onClose();
  };

  const getCurrentStepFields = (): (keyof SPVFormValues)[] => {
    switch (currentStep) {
      case 0:
        return ["name"]; // Only validate required fields
      case 1:
        return []; // No required fields in legal step
      default:
        return [];
    }
  };

  const getProgress = () => {
    return ((currentStep + 1) / formSteps.length) * 100;
  };

  const isLastStep = currentStep === formSteps.length - 1;

  return (
    <Dialog open={isOpen} onOpenChange={handleCancel}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Create New SPV
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={(e) => {
              e.preventDefault();
              // Prevent any form submission - we handle it manually
            }}
            className="space-y-6"
          >
            {/* Error Display */}
            {submitError && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-sm text-red-600">{submitError}</p>
              </div>
            )}

            {/* Admin Organization Selection */}
            {isAdminMode && (
              <FormField
                control={form.control}
                name="organizationId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-1">
                      Organization
                      <InfoButton description="Select the organization this SPV belongs to" />
                    </FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select organization" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {organizations.map((org) => (
                          <SelectItem key={org.id} value={org.id}>
                            {org.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* Step 1: Basic Information */}
            {currentStep === 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Building2 className="h-5 w-5" />
                    Basic Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-1">
                            SPV Name
                            <InfoButton description={fieldDescriptions.name} />
                          </FormLabel>
                          <FormControl>
                            <Input placeholder="Enter SPV name" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                     <FormField
                      control={form.control}
                      name="projectCategories"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-1">
                            Project Category
                            <InfoButton description={fieldDescriptions.projectCategories} />
                          </FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger className="w-full">
                                <SelectValue placeholder="Select project category" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {projectCategories.map((category) => (
                                <SelectItem key={category.value} value={category.value}>
                                  {category.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="adminName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-1">
                            Admin Name
                            <InfoButton description={fieldDescriptions.adminName} />
                          </FormLabel>
                          <FormControl>
                            <Input placeholder="Enter admin name" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />


                    <FormField
                      control={form.control}
                      name="adminEmail"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-1">
                            Admin Email
                            <InfoButton description={fieldDescriptions.adminEmail} />
                          </FormLabel>
                          <FormControl>
                            <Input type="email" placeholder="<EMAIL>" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="purpose"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-1">
                          Purpose
                          <InfoButton description={fieldDescriptions.purpose} />
                        </FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Describe the purpose and objectives of this SPV"
                            className="min-h-[80px]"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            )}

            {/* Step 2: Legal & Compliance */}
            {currentStep === 1 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="h-5 w-5" />
                    Legal & Compliance
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="legalStructure"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-1">
                            Legal Structure
                            <InfoButton description={fieldDescriptions.legalStructure} />
                          </FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger className="w-full">
                                <SelectValue placeholder="Select legal structure" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {legalStructures.map((structure) => (
                                <SelectItem key={structure.value} value={structure.value}>
                                  {structure.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="cinNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-1">
                            CIN Number
                            <InfoButton description={fieldDescriptions.cinNumber} />
                          </FormLabel>
                          <FormControl>
                            <Input placeholder="U12345MH2020PTC123456" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="gstNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-1">
                            GST Number
                            <InfoButton description={fieldDescriptions.gstNumber} />
                          </FormLabel>
                          <FormControl>
                            <Input placeholder="27**********1Z5" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="panNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-1">
                            PAN Number
                            <InfoButton description={fieldDescriptions.panNumber} />
                          </FormLabel>
                          <FormControl>
                            <Input placeholder="**********" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="jurisdiction"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-1">
                            Jurisdiction
                            <InfoButton description={fieldDescriptions.jurisdiction} />
                          </FormLabel>
                          <FormControl>
                            <Input placeholder="Maharashtra" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="country"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-1">
                            Country
                            <InfoButton description={fieldDescriptions.country} />
                          </FormLabel>
                          <FormControl>
                            <Input placeholder="India" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="incorporationDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-1">
                            Incorporation Date
                            <InfoButton description={fieldDescriptions.incorporationDate} />
                          </FormLabel>
                          <FormControl>
                            <Input type="date" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="registeredAddress"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-1">
                          Registered Address
                          <InfoButton description={fieldDescriptions.registeredAddress} />
                        </FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter complete registered address"
                            className="min-h-[80px]"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                </CardContent>
              </Card>
            )}



            {/* Action Buttons */}
            <div className="flex justify-between pt-6 border-t border-border">
              <Button type="button" variant="outline" onClick={handleCancel}>
                Cancel
              </Button>

              <div className="flex gap-3">
                {currentStep > 0 && (
                  <Button type="button" variant="outline" onClick={handlePrevious}>
                    <ChevronLeft className="mr-2 h-4 w-4" />
                    Previous
                  </Button>
                )}

                {!isLastStep ? (
                  <Button type="button" onClick={handleNext}>
                    Next
                    <ChevronRight className="ml-2 h-4 w-4" />
                  </Button>
                ) : (
                  <Button
                    type="button"
                    disabled={isFormSubmitting || isSubmitting}
                    onClick={async () => {
                      const values = form.getValues();
                      await handleSubmit(values);
                    }}
                  >
                    {(isFormSubmitting || isSubmitting) && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    Create SPV
                  </Button>
                )}
              </div>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
