"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Loader2, Save, Settings, Info, Building2, Shield } from "lucide-react";
import { toast } from "sonner";
import { SPVWithDetails } from "@/types/spv";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

// Updated SPV edit schema to match create form structure
const spvEditSchema = z.object({
  // Basic Information - only name is required
  name: z.string().min(1, "SPV name is required"),
  purpose: z.string().optional(),
  legalStructure: z.string().optional(),
  projectCategories: z.string().optional(),
  adminName: z.string().optional(),
  adminEmail: z.string().optional(),

  // Legal & Compliance (all optional)
  jurisdiction: z.string().optional(),
  country: z.string().optional(),
  gstNumber: z.string().optional(),
  cinNumber: z.string().optional(),
  panNumber: z.string().optional(),
  incorporationDate: z.string().optional(),
  registeredAddress: z.string().optional(),
  description: z.string().optional(),
});

type SPVEditFormValues = z.infer<typeof spvEditSchema>;

// Field descriptions for info tooltips
const fieldDescriptions = {
  name: "The official name of your Special Purpose Vehicle",
  purpose: "Brief description of the SPV's business purpose and objectives",
  legalStructure: "The legal form of your organization (e.g., Private Limited Company, LLP)",
  jurisdiction: "The state or territory where the SPV is registered",
  country: "Country of incorporation and operation",
  gstNumber: "Goods and Services Tax identification number (15 digits)",
  cinNumber: "Corporate Identity Number assigned by MCA (21 characters)",
  panNumber: "Permanent Account Number for tax purposes (10 characters)",
  incorporationDate: "Date when the company was officially incorporated",
  registeredAddress: "Official registered address as per incorporation documents",
  projectCategories: "Type of carbon credit projects this SPV will manage",
  adminName: "Name of the SPV administrator",
  adminEmail: "Email address for the SPV administrator account"
};

// Legal structure options
const legalStructures = [
  { value: "private_limited", label: "Private Limited Company" },
  { value: "public_limited", label: "Public Limited Company" },
  { value: "llp", label: "Limited Liability Partnership (LLP)" },
  { value: "partnership", label: "Partnership Firm" },
  { value: "sole_proprietorship", label: "Sole Proprietorship" },
  { value: "trust", label: "Trust" },
  { value: "society", label: "Society" },
  { value: "cooperative", label: "Cooperative Society" },
];

// Project category options
const projectCategoryOptions = [
  { value: "renewable_energy", label: "Renewable Energy" },
  { value: "energy_efficiency", label: "Energy Efficiency" },
  { value: "forestry", label: "Forestry and Land Use" },
  { value: "waste_management", label: "Waste Management" },
  { value: "transportation", label: "Transportation" },
  { value: "industrial", label: "Industrial Processes" },
  { value: "agriculture", label: "Agriculture" },
  { value: "blue_carbon", label: "Blue Carbon" },
];

// Info button component
const InfoButton = ({ description }: { description: string }) => (
  <TooltipProvider>
    <Tooltip>
      <TooltipTrigger asChild>
        <Info className="h-3 w-3 text-muted-foreground cursor-help ml-1 bg-white rounded-full p-0.5 border border-gray-200" />
      </TooltipTrigger>
      <TooltipContent className="max-w-xs !bg-white !border !border-gray-200 !text-gray-900 shadow-lg">
        <p className="text-sm">{description}</p>
      </TooltipContent>
    </Tooltip>
  </TooltipProvider>
);

interface SPVEditFormProps {
  spv: SPVWithDetails;
  onSuccess?: () => void;
}

export function SPVEditForm({ spv, onSuccess }: SPVEditFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);

  const form = useForm<SPVEditFormValues>({
    resolver: zodResolver(spvEditSchema),
    defaultValues: {
      name: spv.name,
      purpose: spv.purpose || "",
      legalStructure: spv.legalStructure || "",
      projectCategories: spv.projectCategories[0] || "",
      adminName: "", // Not stored in SPV model, would need to get from SPV admin user
      adminEmail: "", // Not stored in SPV model, would need to get from SPV admin user
      jurisdiction: spv.jurisdiction || "",
      country: spv.country || "",
      gstNumber: spv.gstNumber || "",
      cinNumber: spv.cinNumber || "",
      panNumber: spv.panNumber || "",
      incorporationDate: spv.incorporationDate ? spv.incorporationDate.split('T')[0] : "",
      registeredAddress: spv.registeredAddress || "",
      description: spv.description || "",
    },
  });

  const handleNext = () => {
    if (currentStep < 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const isLastStep = currentStep === 1;

  const handleSubmit = async (data: SPVEditFormValues) => {
    try {
      setIsSubmitting(true);

      const response = await fetch(`/api/organizations/spvs/${spv.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        toast.success("SPV updated successfully");
        onSuccess?.();
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || "Failed to update SPV");
      }
    } catch (error) {
      console.error("Error updating SPV:", error);
      toast.error("Failed to update SPV");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          Edit SPV Information
        </CardTitle>
      </CardHeader>
      <CardContent>

        <Form {...form}>
          <form
            onSubmit={(e) => {
              e.preventDefault();
              // Prevent any form submission - we handle it manually
            }}
            className="space-y-6"
          >
            {/* Step 1: Basic Information */}
            {currentStep === 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Building2 className="h-5 w-5" />
                    Basic Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-1">
                            SPV Name
                            <InfoButton description={fieldDescriptions.name} />
                          </FormLabel>
                          <FormControl>
                            <Input placeholder="Enter SPV name" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="projectCategories"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-1">
                            Project Category
                            <InfoButton description={fieldDescriptions.projectCategories} />
                          </FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger className="w-full">
                                <SelectValue placeholder="Select project category" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {projectCategoryOptions.map((category) => (
                                <SelectItem key={category.value} value={category.value}>
                                  {category.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="adminName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-1">
                            Admin Name
                            <InfoButton description={fieldDescriptions.adminName} />
                          </FormLabel>
                          <FormControl>
                            <Input placeholder="Enter admin name" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="adminEmail"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-1">
                            Admin Email
                            <InfoButton description={fieldDescriptions.adminEmail} />
                          </FormLabel>
                          <FormControl>
                            <Input type="email" placeholder="<EMAIL>" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="purpose"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-1">
                          Purpose
                          <InfoButton description={fieldDescriptions.purpose} />
                        </FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Describe the purpose and objectives of this SPV"
                            className="min-h-[80px]"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            )}

            {/* Step 2: Legal & Compliance */}
            {currentStep === 1 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="h-5 w-5" />
                    Legal & Compliance
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="legalStructure"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-1">
                            Legal Structure
                            <InfoButton description={fieldDescriptions.legalStructure} />
                          </FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger className="w-full">
                                <SelectValue placeholder="Select legal structure" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {legalStructures.map((structure) => (
                                <SelectItem key={structure.value} value={structure.value}>
                                  {structure.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="cinNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-1">
                            CIN Number
                            <InfoButton description={fieldDescriptions.cinNumber} />
                          </FormLabel>
                          <FormControl>
                            <Input placeholder="L12345MH2020PTC123456" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="gstNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-1">
                            GST Number
                            <InfoButton description={fieldDescriptions.gstNumber} />
                          </FormLabel>
                          <FormControl>
                            <Input placeholder="22**********1Z5" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="panNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-1">
                            PAN Number
                            <InfoButton description={fieldDescriptions.panNumber} />
                          </FormLabel>
                          <FormControl>
                            <Input placeholder="**********" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="jurisdiction"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-1">
                            Jurisdiction
                            <InfoButton description={fieldDescriptions.jurisdiction} />
                          </FormLabel>
                          <FormControl>
                            <Input placeholder="Maharashtra" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="country"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-1">
                            Country
                            <InfoButton description={fieldDescriptions.country} />
                          </FormLabel>
                          <FormControl>
                            <Input placeholder="India" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="incorporationDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-1">
                            Incorporation Date
                            <InfoButton description={fieldDescriptions.incorporationDate} />
                          </FormLabel>
                          <FormControl>
                            <Input type="date" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="registeredAddress"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-1">
                            Registered Address
                            <InfoButton description={fieldDescriptions.registeredAddress} />
                          </FormLabel>
                          <FormControl>
                            <Input placeholder="Complete registered address" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-1">
                          Additional Description
                          <InfoButton description="Any additional information about the SPV" />
                        </FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter any additional information"
                            className="min-h-[80px]"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            )}

            {/* Form Navigation */}
            <div className="flex justify-between pt-6 border-t border-border">
              <Button type="button" variant="outline" onClick={() => setCurrentStep(0)}>
                Cancel
              </Button>

              <div className="flex gap-3">
                {currentStep > 0 && (
                  <Button type="button" variant="outline" onClick={handlePrevious}>
                    Previous
                  </Button>
                )}

                {!isLastStep ? (
                  <Button type="button" onClick={handleNext}>
                    Next
                  </Button>
                ) : (
                  <Button
                    type="button"
                    disabled={isSubmitting}
                    onClick={async () => {
                      const values = form.getValues();
                      await handleSubmit(values);
                    }}
                  >
                    {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </Button>
                )}
              </div>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
