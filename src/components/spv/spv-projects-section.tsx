"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { 
  Folder, 
  Plus, 
  Calendar,
  MoreHorizontal,
  Edit,
  Trash2,
  FolderPlus,
  MapPin,
  Zap
} from "lucide-react";
import { toast } from "sonner";
import { SPVWithDetails } from "@/types/spv";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface Project {
  id: string;
  name: string;
  status: string;
  type?: string;
  createdAt: string;
  estimatedReductions?: number | null;
  location?: string;
  description?: string;
}

interface SPVProjectsSectionProps {
  spv: SPVWithDetails;
}

export function SPVProjectsSection({ spv }: SPVProjectsSectionProps) {
  const [projects, setProjects] = useState<Project[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAssignProjectOpen, setIsAssignProjectOpen] = useState(false);

  useEffect(() => {
    fetchProjects();
  }, [spv.id]);

  const fetchProjects = async () => {
    try {
      setIsLoading(true);
      // Use the existing spv data if available
      if (spv.projects) {
        setProjects(spv.projects as Project[]);
      } else {
        // Fallback to API call if needed
        const response = await fetch(`/api/organizations/spvs/${spv.id}/projects`);
        if (response.ok) {
          const data = await response.json();
          setProjects(data.projects);
        } else {
          toast.error("Failed to fetch SPV projects");
        }
      }
    } catch (error) {
      console.error("Error fetching projects:", error);
      toast.error("Failed to fetch SPV projects");
    } finally {
      setIsLoading(false);
    }
  };

  const handleProjectAssigned = () => {
    fetchProjects();
    setIsAssignProjectOpen(false);
    toast.success("Project assigned successfully!");
  };

  const handleUnassignProject = async (projectId: string) => {
    if (!confirm("Are you sure you want to unassign this project from the SPV?")) {
      return;
    }

    try {
      const response = await fetch(`/api/organizations/spvs/${spv.id}/projects/${projectId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        toast.success("Project unassigned successfully");
        fetchProjects();
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || "Failed to unassign project");
      }
    } catch (error) {
      console.error("Error unassigning project:", error);
      toast.error("Failed to unassign project");
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "completed":
        return "bg-blue-100 text-blue-800";
      case "inactive":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getTypeIcon = (type?: string) => {
    switch (type?.toLowerCase()) {
      case "solar":
        return <Zap className="h-4 w-4 text-yellow-600" />;
      case "wind":
        return <Zap className="h-4 w-4 text-blue-600" />;
      case "hybrid":
        return <Zap className="h-4 w-4 text-purple-600" />;
      default:
        return <Folder className="h-4 w-4 text-gray-600" />;
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="h-20 bg-gray-200 rounded"></div>
            <div className="h-20 bg-gray-200 rounded"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Folder className="h-5 w-5" />
                Assigned Projects ({projects.length})
              </CardTitle>
              <CardDescription>
                Projects assigned to this SPV for carbon credit management
              </CardDescription>
            </div>
            {/* Commented out assign project button as requested */}
            {/* <Dialog open={isAssignProjectOpen} onOpenChange={setIsAssignProjectOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Assign Project
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Assign Project to {spv.name}</DialogTitle>
                </DialogHeader>
                <div className="p-4 text-center text-muted-foreground">
                  Project assignment form coming soon...
                </div>
              </DialogContent>
            </Dialog> */}
          </div>
        </CardHeader>
        <CardContent>
          {projects.length === 0 ? (
            <div className="text-center py-8">
              <FolderPlus className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No projects assigned</h3>
              <p className="text-muted-foreground mb-4">
                Assign projects to this SPV to start managing carbon credits
              </p>
              {/* Commented out assign project button */}
              {/* <Button onClick={() => setIsAssignProjectOpen(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Assign First Project
              </Button> */}
            </div>
          ) : (
            <div className="space-y-4">
              {projects.map((project) => (
                <Card key={project.id} className="border-l-4 border-l-green-500">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-4">
                        <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                          {getTypeIcon(project.type)}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="font-semibold">{project.name}</h3>
                            <Badge className={getStatusColor(project.status)}>
                              {project.status}
                            </Badge>
                            {project.type && (
                              <Badge variant="outline">
                                {project.type}
                              </Badge>
                            )}
                          </div>
                          
                          <div className="space-y-1 text-sm text-muted-foreground">
                            {project.description && (
                              <p className="text-sm">{project.description}</p>
                            )}
                            {project.location && (
                              <div className="flex items-center gap-2">
                                <MapPin className="h-4 w-4" />
                                {project.location}
                              </div>
                            )}
                            {project.estimatedReductions && (
                              <div className="flex items-center gap-2">
                                <Zap className="h-4 w-4" />
                                {project.estimatedReductions.toLocaleString()} tCO2e estimated
                              </div>
                            )}
                            <div className="flex items-center gap-2">
                              <Calendar className="h-4 w-4" />
                              Created {new Date(project.createdAt).toLocaleDateString()}
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>
                            <Edit className="mr-2 h-4 w-4" />
                            View Project
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            className="text-destructive"
                            onClick={() => handleUnassignProject(project.id)}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Unassign Project
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
