"use client";

import { Building2, Calendar, MapPin, FileText, MoreHorizontal, Edit, Trash2, Eye, Shield, AlertCircle, Check, Clock, X, Upload } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { SPV, SPVWithOrganization, SPVVerificationStatus } from "@/types/spv";
import { spvUtils } from "@/lib/api/spv";

interface SPVCardProps {
  spv: SPV | SPVWithOrganization;
  onEdit?: (spv: SPV | SPVWithOrganization) => void;
  onDelete?: (spv: SPV | SPVWithOrganization) => void;
  onView?: (spv: SPV | SPVWithOrganization) => void;
  onCompleteVerification?: (spv: SPV | SPVWithOrganization) => void;
  onUploadDocuments?: (spv: SPV | SPVWithOrganization) => void;
  showOrganization?: boolean;
  showActions?: boolean;
}

// Verification flow commented out for now
// Helper function to get verification status styling
// function getVerificationStatusStyle(status: SPVVerificationStatus) {
//   switch (status) {
//     case "VERIFIED":
//       return {
//         className: "bg-green-100 text-green-800 border-green-200",
//         icon: Check,
//         label: "Verified"
//       };
//     case "REJECTED":
//       return {
//         className: "bg-red-100 text-red-800 border-red-200",
//         icon: X,
//         label: "Rejected"
//       };
//     case "IN_REVIEW":
//       return {
//         className: "bg-blue-100 text-blue-800 border-blue-200",
//         icon: Eye,
//         label: "In Review"
//       };
//     case "NEEDS_MORE_INFO":
//       return {
//         className: "bg-yellow-100 text-yellow-800 border-yellow-200",
//         icon: AlertCircle,
//         label: "Needs Info"
//       };
//     case "PENDING_VERIFICATION":
//       return {
//         className: "bg-gray-100 text-gray-800 border-gray-200",
//         icon: Clock,
//         label: "Pending"
//       };
//     default:
//       return {
//         className: "bg-gray-100 text-gray-800 border-gray-200",
//         icon: Shield,
//         label: "Unknown"
//       };
//   }
// }

export function SPVCard({
  spv,
  onEdit,
  onDelete,
  onView,
  onCompleteVerification,
  onUploadDocuments,
  showOrganization = false,
  showActions = true,
}: SPVCardProps) {
  const isWithOrganization = 'organization' in spv;
  // Verification flow commented out for now
  // const verificationStyle = spv.verificationStatus ? getVerificationStatusStyle(spv.verificationStatus) : null;
  const verificationStyle = null;

  return (
    <Card
      className={`hover:shadow-md transition-shadow ${onView ? 'cursor-pointer' : ''}`}
      onClick={onView ? () => onView(spv) : undefined}
    >
      <CardHeader>
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <CardTitle className="text-lg flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              {spv.name}
            </CardTitle>
            <CardDescription className="mt-1">
              {spv.legalStructure && spv.jurisdiction ? (
                `${spv.legalStructure} • ${spv.jurisdiction}`
              ) : (
                spv.legalStructure || spv.jurisdiction || "No structure specified"
              )}
            </CardDescription>
            {showOrganization && isWithOrganization && (
              <CardDescription className="mt-1 text-xs">
                {spv.organization.name}
              </CardDescription>
            )}
          </div>
          
          <div className="flex items-center gap-2 flex-wrap">
            {/* Verification Status Badge - Commented out for now */}
            {/* {verificationStyle && (
              <Badge
                variant="outline"
                className={`text-xs ${verificationStyle.className}`}
              >
                <verificationStyle.icon className="h-3 w-3 mr-1" />
                {verificationStyle.label}
              </Badge>
            )} */}

            {/* SPV Status Badge */}
            <Badge
              variant={spv.status === "ACTIVE" ? "default" : "secondary"}
              className={
                spv.status === "ACTIVE" ? "bg-green-100 text-green-800" :
                spv.status === "PENDING" ? "bg-yellow-100 text-yellow-800" :
                spv.status === "INACTIVE" ? "bg-gray-100 text-gray-800" :
                "bg-red-100 text-red-800"
              }
            >
              {spvUtils.formatStatus(spv.status)}
            </Badge>

            {showActions && (
              <div className="flex items-center gap-2">
                {/* Quick Action Buttons - Verification flow commented out */}
                {/* {spv.verificationStatus === "PENDING_VERIFICATION" && onCompleteVerification && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => onCompleteVerification(spv)}
                    className="text-xs h-7"
                  >
                    <Shield className="h-3 w-3 mr-1" />
                    Complete
                  </Button>
                )} */}

                {/* {(spv.verificationStatus === "PENDING_VERIFICATION" || spv.verificationStatus === "NEEDS_MORE_INFO") && onUploadDocuments && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => onUploadDocuments(spv)}
                    className="text-xs h-7"
                  >
                    <Upload className="h-3 w-3 mr-1" />
                    Upload
                  </Button>
                )} */}

                <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {onView && (
                    <DropdownMenuItem onClick={() => onView(spv)}>
                      <Eye className="mr-2 h-4 w-4" />
                      View Details
                    </DropdownMenuItem>
                  )}

                  {/* Verification Actions */}
                  {/* Verification flow commented out for now */}
                  {/* {spv.verificationStatus === "PENDING_VERIFICATION" && onCompleteVerification && (
                    <DropdownMenuItem onClick={() => onCompleteVerification(spv)}>
                      <Shield className="mr-2 h-4 w-4" />
                      Complete Verification
                    </DropdownMenuItem>
                  )} */}

                  {/* {(spv.verificationStatus === "PENDING_VERIFICATION" || spv.verificationStatus === "NEEDS_MORE_INFO") && onUploadDocuments && (
                    <DropdownMenuItem onClick={() => onUploadDocuments(spv)}>
                      <Upload className="mr-2 h-4 w-4" />
                      Upload Documents
                    </DropdownMenuItem>
                  )} */}

                  {onEdit && (
                    <DropdownMenuItem onClick={() => onEdit(spv)}>
                      <Edit className="mr-2 h-4 w-4" />
                      Edit Basic Info
                    </DropdownMenuItem>
                  )}
                  {onDelete && (
                    <DropdownMenuItem
                      className="text-destructive"
                      onClick={() => onDelete(spv)}
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
                </DropdownMenu>
              </div>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {/* Purpose */}
        {spv.purpose && (
          <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
            {spv.purpose}
          </p>
        )}
        
        {/* Details */}
        <div className="space-y-2 text-sm">
          {spv.establishedDate && (
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium">Established:</span>
              <span>{spvUtils.formatDate(spv.establishedDate)}</span>
            </div>
          )}
          
          {spv.registrationNumber && (
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium">Registration:</span>
              <span>{spv.registrationNumber}</span>
            </div>
          )}
          
          {spv.address && (
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium">Address:</span>
              <span className="line-clamp-1">{spv.address}</span>
            </div>
          )}
          
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <span className="font-medium">Created:</span>
            <span>{spvUtils.formatDate(spv.createdAt)}</span>
          </div>

          {/* Verification Progress - Commented out for now */}
          {/* {spv.verificationStatus && spv.verificationStatus !== "VERIFIED" && (
            <div className="mt-3 pt-3 border-t border-border">
              <div className="flex items-center gap-2 mb-2">
                <Shield className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium text-xs">Next Steps</span>
              </div>
              <div className="text-xs text-muted-foreground space-y-1">
                {spv.verificationStatus === "PENDING_VERIFICATION" && (
                  <div className="space-y-1">
                    <div className="flex items-center gap-1">
                      <div className="w-1 h-1 bg-blue-500 rounded-full"></div>
                      <span>Complete verification details</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="w-1 h-1 bg-blue-500 rounded-full"></div>
                      <span>Upload required documents</span>
                    </div>
                  </div>
                )}
                {spv.verificationStatus === "IN_REVIEW" && "Under review by verification team"}
                {spv.verificationStatus === "NEEDS_MORE_INFO" && (
                  <div className="space-y-1">
                    <div className="flex items-center gap-1">
                      <div className="w-1 h-1 bg-yellow-500 rounded-full"></div>
                      <span>Provide additional information</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="w-1 h-1 bg-yellow-500 rounded-full"></div>
                      <span>Upload missing documents</span>
                    </div>
                  </div>
                )}
                {spv.verificationStatus === "REJECTED" && "Verification rejected - review feedback"}
              </div>
            </div>
          )} */}

          {/* Contact Information for verified SPVs - Commented out for now */}
          {/* {spv.verificationStatus === "VERIFIED" && spv.contactPersonEmail && (
            <div className="mt-3 pt-3 border-t border-border">
              <div className="flex items-center gap-2 mb-1">
                <Shield className="h-4 w-4 text-green-600" />
                <span className="font-medium text-xs text-green-600">Verified SPV</span>
              </div>
              <div className="text-xs text-muted-foreground">
                Contact: {spv.contactPersonEmail}
              </div>
            </div>
          )} */}
        </div>
      </CardContent>
    </Card>
  );
}

interface SPVCardGridProps {
  spvs: (SPV | SPVWithOrganization)[];
  onEdit?: (spv: SPV | SPVWithOrganization) => void;
  onDelete?: (spv: SPV | SPVWithOrganization) => void;
  onView?: (spv: SPV | SPVWithOrganization) => void;
  onCompleteVerification?: (spv: SPV | SPVWithOrganization) => void;
  onUploadDocuments?: (spv: SPV | SPVWithOrganization) => void;
  showOrganization?: boolean;
  showActions?: boolean;
  isLoading?: boolean;
  emptyState?: {
    title: string;
    description: string;
    action?: React.ReactNode;
  };
}

export function SPVCardGrid({
  spvs,
  onEdit,
  onDelete,
  onView,
  onCompleteVerification,
  onUploadDocuments,
  showOrganization = false,
  showActions = true,
  isLoading = false,
  emptyState,
}: SPVCardGridProps) {
  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {[...Array(6)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="h-6 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-full"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (spvs.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Building2 className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">
            {emptyState?.title || "No SPVs found"}
          </h3>
          <p className="text-muted-foreground text-center mb-4">
            {emptyState?.description || "No SPVs have been created yet"}
          </p>
          {emptyState?.action}
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {spvs.map((spv) => (
        <SPVCard
          key={spv.id}
          spv={spv}
          onEdit={onEdit}
          onDelete={onDelete}
          onView={onView}
          onCompleteVerification={onCompleteVerification}
          onUploadDocuments={onUploadDocuments}
          showOrganization={showOrganization}
          showActions={showActions}
        />
      ))}
    </div>
  );
}
