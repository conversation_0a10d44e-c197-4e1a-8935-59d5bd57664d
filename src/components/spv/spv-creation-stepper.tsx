"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";

const spvCreationSchema = z.object({
  name: z.string().min(3, "SPV name must be at least 3 characters").max(100, "SPV name must be less than 100 characters"),
  country: z.string().min(1, "Country is required"),
  legalEntityId: z.string().min(1, "Legal Entity ID is required"),
  contact: z.string().min(1, "Contact information is required"),
  projectCategories: z.string().min(1, "Project category is required"),
  adminEmail: z.string().email("Invalid email address"),
  purpose: z.string().max(500, "Purpose must be less than 500 characters").optional(),
});

type SPVCreationFormValues = z.infer<typeof spvCreationSchema>;

interface SPVCreationStepperProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (data: any) => void;
}

export function SPVCreationStepper({
  isOpen,
  onClose,
  onSuccess,
}: SPVCreationStepperProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<SPVCreationFormValues>({
    resolver: zodResolver(spvCreationSchema),
    defaultValues: {
      name: "",
      country: "",
      legalEntityId: "",
      contact: "",
      projectCategories: "",
      adminEmail: "",
      purpose: "",
    },
  });

  const handleSubmit = async (data: SPVCreationFormValues) => {
    try {
      setIsSubmitting(true);

      // Debug: Log the data being sent
      console.log("SPV Creation Data:", data);

      const response = await fetch("/api/organizations/spvs", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        const result = await response.json();
        toast.success("SPV created successfully! Admin credentials have been sent to the provided email.");
        onSuccess(result);
        handleClose();
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || "Failed to create SPV");
      }
    } catch (error) {
      console.error("Error creating SPV:", error);
      toast.error("Failed to create SPV");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      form.reset();
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create SPV with Admin</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>SPV Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>SPV Name *</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter SPV name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="adminEmail"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Admin Email *</FormLabel>
                        <FormControl>
                          <Input
                            type="email"
                            placeholder="<EMAIL>"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="country"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Country *</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., United States, Singapore" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="legalEntityId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Legal Entity ID *</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter legal entity identifier" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="contact"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Contact Information *</FormLabel>
                        <FormControl>
                          <Input placeholder="Contact person or phone number" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="projectCategories"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Project Category *</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select project category" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="Solar">Solar</SelectItem>
                            <SelectItem value="Wind">Wind</SelectItem>
                            <SelectItem value="Hybrid">Hybrid</SelectItem>
                            <SelectItem value="Forestry">Forestry</SelectItem>
                            <SelectItem value="Energy Efficiency">Energy Efficiency</SelectItem>
                            <SelectItem value="Waste Management">Waste Management</SelectItem>
                            <SelectItem value="Transportation">Transportation</SelectItem>
                            <SelectItem value="Other">Other</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <FormField
                  control={form.control}
                  name="purpose"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Purpose</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Describe the purpose of this SPV (optional)"
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
            <div className="flex justify-end gap-3">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
              >
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Create SPV & Send Credentials
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
