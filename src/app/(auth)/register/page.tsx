import { Metada<PERSON> } from "next";
import Link from "next/link";
import { Suspense } from "react";
import { RegisterForm } from "./register-form";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";

export const metadata: Metadata = {
  title: "Register | Carbonix",
  description: "Create a new Carbonix account",
};

export default function RegisterPage() {
  return (
    <div className="flex w-full flex-col items-center justify-center flex-grow">
      <div className="mx-auto flex w-full flex-col space-y-6 sm:w-[400px]">
        <Card className="w-full">
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl font-semibold tracking-tight text-center">
              Create an account
            </CardTitle>
            <CardDescription className="text-center">
              Enter your details to create a new account
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Suspense fallback={
              <div className="flex justify-center items-center py-8">
                <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
              </div>
            }>
              <RegisterForm />
            </Suspense>
          </CardContent>
          <CardFooter className="flex justify-center">
            <p className="text-center text-sm text-muted-foreground">
              <Link
                href="/login"
                className="underline underline-offset-4 transition-none hover:text-foreground"
              >
                Already have an account? Sign In
              </Link>
            </p>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
