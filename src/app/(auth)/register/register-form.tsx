"use client";

import { useR<PERSON>er, useSearch<PERSON>arams } from "next/navigation";
import { useState } from "react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle, Eye, EyeOff, Loader2 } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ValidatedForm } from "@/components/forms/validated-form";
import { registrationSchema } from "@/lib/validation/schemas";
import { FormField, FormItem, FormControl, FormMessage } from "@/components/ui/form";

type RegisterFormValues = {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
};

export function RegisterForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Get pre-filled data from URL parameters
  const prefilledName = searchParams.get("name") || "";
  const prefilledEmail = searchParams.get("email") || "";
  const isEdit = searchParams.get("edit") === "true";

  const handleRegister = async (data: RegisterFormValues) => {
    try {
      const response = await fetch("/api/register", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRF-Token": "bypass-for-registration",
        },
        body: JSON.stringify({
          name: data?.name,
          email: data?.email,
          password: data?.password,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "An error occurred during registration");
      }

      if (result.requiresOtpVerification) {
        // Pass both email and name to OTP verification page
        const params = new URLSearchParams();
        params.set("email", data.email);
        params.set("name", data.name);
        router.push(`/verify-otp?${params.toString()}`);
      } else {
        router.push("/login?registered=true");
      }
    } catch (error) {
      console.error("Registration error:", error);
      throw error;
    }
  };

  return (
    <ValidatedForm
      schema={registrationSchema}
      defaultValues={{
        name: prefilledName,
        email: prefilledEmail,
        password: "",
        confirmPassword: ""
      }}
      onSubmit={(data: RegisterFormValues) => {
        return handleRegister(data);
      }}
      className="space-y-4"
      showErrorSummary={false}
      formOptions={{
        showToast: false,
        mode: "onTouched",
        reValidateMode: "onChange",
      }}
    >
      {({ control, isSubmitting, formError }) => (
        <div className="space-y-4">
          {/* Show edit mode indicator */}
          {isEdit && (
            <Alert className="py-2 bg-blue-50 border-blue-200">
              <AlertCircle className="h-4 w-4 text-blue-500" />
              <AlertDescription className="text-blue-700">
                Update your information below and create your account again.
              </AlertDescription>
            </Alert>
          )}

          {/* Display server/submission errors */}
          {formError && (
            <Alert variant="destructive" className="py-2">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{formError.message}</AlertDescription>
            </Alert>
          )}

          {/* Name Field */}
          <FormField
            control={control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <Label htmlFor="name">Name *</Label>
                <FormControl>
                  <Input
                    id="name"
                    placeholder="Enter your full name"
                    type="text"
                    autoCapitalize="none"
                    autoCorrect="off"
                    disabled={isSubmitting}
                    className="transition-none hover:border-input"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Email Field */}
          <FormField
            control={control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <Label htmlFor="email">Email *</Label>
                <FormControl>
                  <Input
                    id="email"
                    placeholder="Enter your email"
                    type="email"
                    autoCapitalize="none"
                    autoComplete="email"
                    autoCorrect="off"
                    disabled={isSubmitting}
                    className="transition-none hover:border-input"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Password Field */}
          <FormField
            control={control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <Label htmlFor="password">Password *</Label>
                <FormControl>
                  <div className="relative">
                    <Input
                      id="password"
                      placeholder="Enter your password"
                      type={showPassword ? "text" : "password"}
                      autoComplete="new-password"
                      disabled={isSubmitting}
                      className="pr-10 transition-none hover:border-input"
                      {...field}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4 text-muted-foreground" />
                      ) : (
                        <Eye className="h-4 w-4 text-muted-foreground" />
                      )}
                    </Button>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Confirm Password Field */}
          <FormField
            control={control}
            name="confirmPassword"
            render={({ field }) => (
              <FormItem>
                <Label htmlFor="confirmPassword">Confirm Password *</Label>
                <FormControl>
                  <div className="relative">
                    <Input
                      id="confirmPassword"
                      placeholder="Confirm your password"
                      type={showConfirmPassword ? "text" : "password"}
                      autoComplete="new-password"
                      disabled={isSubmitting}
                      className="pr-10 transition-none hover:border-input"
                      {...field}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    >
                      {showConfirmPassword ? (
                        <EyeOff className="h-4 w-4 text-muted-foreground" />
                      ) : (
                        <Eye className="h-4 w-4 text-muted-foreground" />
                      )}
                    </Button>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button
            type="submit"
            className="w-full transition-none hover:bg-primary"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Creating account...
              </>
            ) : (
              "Create account"
            )}
          </Button>
        </div>
      )}
    </ValidatedForm>
  );
}
