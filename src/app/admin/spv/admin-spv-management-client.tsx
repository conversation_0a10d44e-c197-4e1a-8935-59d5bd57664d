"use client";

import { useState } from "react";
import { Plus, Building2, Search, Filter, MoreHorizontal, Edit, Trash2, Eye, Users, ChevronDown } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from "@/components/ui/dropdown-menu";
import { useAdminSPVs, useAdminSPVDetails, useAdminSPVMutations } from "@/hooks/use-admin-spvs";
import { spvUtils } from "@/lib/api/spv";
import { SPVStatus, SPVWithOrganization, AdminSPVCreateData, SPVUpdateData } from "@/types/spv";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { SPVDataTable } from "@/components/spv/spv-data-table";
import { SPVFilters, SPVStats } from "@/components/spv/spv-filters";
import { EnhancedSPVCreationForm } from "@/components/spv/enhanced-spv-creation-form";
import { AdminSPVUserForm } from "@/components/spv/admin-spv-user-form";
import { SPVDetailsView } from "@/components/spv/spv-details-view";
import { toast } from "sonner";

export default function AdminSPVManagementClient() {
  const {
    spvs,
    organizations,
    pagination,
    filters,
    isLoading,
    error,
    refetch,
    updateFilters,
  } = useAdminSPVs();

  const { createSPV, updateSPV, deleteSPV, isCreating, isUpdating, isDeleting } = useAdminSPVMutations(refetch);

  const [selectedSPV, setSelectedSPV] = useState<SPVWithOrganization | null>(null);
  const [detailsSPVId, setDetailsSPVId] = useState<string | null>(null);
  const [isCreateFormOpen, setIsCreateFormOpen] = useState(false);
  const [isCreateUserFormOpen, setIsCreateUserFormOpen] = useState(false);
  const [isEditFormOpen, setIsEditFormOpen] = useState(false);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);

  const { spv: detailsSPV, isLoading: isLoadingDetails } = useAdminSPVDetails(detailsSPVId || "");

  const handleCreateSPV = async (data: AdminSPVCreateData) => {
    try {
      const result = await createSPV(data);

      // Close modal immediately
      setIsCreateFormOpen(false);
    } catch (error) {
      toast.error("Failed to create SPV");
    }
  };

  const handleUpdateSPV = async (data: SPVUpdateData) => {
    if (!selectedSPV) return;

    try {
      await updateSPV(selectedSPV.id, data);
      toast.success("SPV updated successfully");
      setIsEditFormOpen(false);
      setSelectedSPV(null);
    } catch (error) {
      toast.error("Failed to update SPV");
    }
  };

  const handleCreateSPVUser = async (data: any) => {
    try {
      const response = await fetch("/api/admin/spv-users", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create SPV user");
      }

      const result = await response.json();
      toast.success(`SPV user "${data.name}" created successfully!`);

      // Log credentials for admin reference
      console.log("SPV User Created:", {
        email: data.email,
        password: data.password,
        role: data.role,
        spv: result.spvUser?.spv?.name
      });

      setIsCreateUserFormOpen(false);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to create SPV user");
      throw error;
    }
  };

  const handleDeleteSPV = async (spv: SPVWithOrganization) => {
    if (!confirm(`Are you sure you want to delete "${spv.name}"? This action cannot be undone.`)) {
      return;
    }

    try {
      await deleteSPV(spv.id);
      toast.success("SPV deleted successfully");
    } catch (error) {
      toast.error("Failed to delete SPV");
    }
  };

  const handleViewSPV = (spv: SPVWithOrganization) => {
    setDetailsSPVId(spv.id);
    setIsDetailsOpen(true);
  };

  const handleEditSPV = (spv: SPVWithOrganization) => {
    setSelectedSPV(spv);
    setIsEditFormOpen(true);
  };

  const handleSort = (sortBy: string) => {
    const newSortOrder = filters.sortBy === sortBy && filters.sortOrder === "asc" ? "desc" : "asc";
    updateFilters({ sortBy, sortOrder: newSortOrder });
  };

  const handlePageChange = (page: number) => {
    updateFilters({ page });
  };

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>
          Error loading SPVs: {error}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">SPV Management</h1>
          <p className="text-muted-foreground">
            Manage Special Purpose Vehicles across all organizations
          </p>
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Create
              <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem onClick={() => setIsCreateFormOpen(true)}>
              <Building2 className="mr-2 h-4 w-4" />
              Create SPV
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => setIsCreateUserFormOpen(true)}>
              <Users className="mr-2 h-4 w-4" />
              Create SPV User
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Filters */}
      <SPVFilters
        filters={filters}
        organizations={organizations}
        onFiltersChange={updateFilters}
        showOrganizationFilter={true}
      />

      {/* Stats */}
      <SPVStats
        totalSPVs={pagination.totalCount}
        activeSPVs={spvs.filter(spv => spv.status === "ACTIVE").length}
        pendingSPVs={spvs.filter(spv => spv.status === "PENDING").length}
        inactiveSPVs={spvs.filter(spv => spv.status === "INACTIVE").length}
        dissolvedSPVs={spvs.filter(spv => spv.status === "DISSOLVED").length}
        organizationCount={organizations.length}
      />

      {/* SPV Table */}
      <SPVDataTable
        spvs={spvs}
        pagination={pagination}
        filters={filters}
        onSort={handleSort}
        onPageChange={handlePageChange}
        onEdit={handleEditSPV}
        onDelete={handleDeleteSPV}
        onView={handleViewSPV}
        showOrganization={true}
        showActions={true}
        isLoading={isLoading}
      />

      {/* Create SPV Form */}
      <EnhancedSPVCreationForm
        isOpen={isCreateFormOpen}
        onClose={() => setIsCreateFormOpen(false)}
        onSubmit={handleCreateSPV}
        organizations={organizations}
        mode="admin"
        isSubmitting={isCreating}
      />

      {/* Create SPV User Form */}
      <AdminSPVUserForm
        isOpen={isCreateUserFormOpen}
        onClose={() => setIsCreateUserFormOpen(false)}
        onSubmit={handleCreateSPVUser}
        organizations={organizations}
        spvs={spvs}
        isSubmitting={false}
      />

      {/* Edit SPV Form */}
      {selectedSPV && (
        <AdminSPVForm
          isOpen={isEditFormOpen}
          onClose={() => {
            setIsEditFormOpen(false);
            setSelectedSPV(null);
          }}
          onSubmit={handleUpdateSPV}
          organizations={organizations}
          initialData={{
            ...selectedSPV,
            establishedDate: selectedSPV.establishedDate || "",
          }}
          mode="edit"
          isSubmitting={isUpdating}
        />
      )}

      {/* SPV Details View */}
      {detailsSPV && (
        <SPVDetailsView
          spv={detailsSPV}
          isOpen={isDetailsOpen}
          onClose={() => {
            setIsDetailsOpen(false);
            setDetailsSPVId(null);
          }}
          onEdit={() => {
            setSelectedSPV(detailsSPV);
            setIsDetailsOpen(false);
            setIsEditFormOpen(true);
          }}
          onDelete={() => {
            handleDeleteSPV(detailsSPV);
            setIsDetailsOpen(false);
          }}
          showActions={true}
        />
      )}
    </div>
  );
}
