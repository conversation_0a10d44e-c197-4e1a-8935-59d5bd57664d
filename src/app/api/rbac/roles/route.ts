import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { z } from 'zod';
import { logger } from '@/lib/logger';
import { hasPermission } from '@/lib/rbac/rbac-service';
import { PermissionContext } from '@/lib/rbac/types';
import { SYSTEM_ROLES, PREDEFINED_CUSTOM_ROLES } from '@/lib/rbac/roles';
import { ALL_PERMISSIONS } from '@/lib/rbac/permissions';

// Schema for role creation
const createRoleSchema = z.object({
  name: z.string().min(1, 'Role name is required').max(50, 'Role name too long'),
  description: z.string().optional(),
  permissions: z.array(z.string()).default([]),
  parentRoleId: z.string().optional(),
});

// Schema for role update
const updateRoleSchema = createRoleSchema.partial();

/**
 * GET /api/rbac/roles
 * Get all roles for the organization (system, predefined, and custom)
 */
export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's organization
    const user = await db.user.findUnique({
      where: { id: session.user.id },
      select: { organizationId: true },
    });

    if (!user?.organizationId) {
      return NextResponse.json({ error: 'User not associated with organization' }, { status: 400 });
    }

    // Check permission
    const context: PermissionContext = {
      userId: session.user.id,
      organizationId: user.organizationId,
    };

    const canRead = await hasPermission('read:role', context);
    if (!canRead) {
      return NextResponse.json({ error: 'Permission denied' }, { status: 403 });
    }

    // Get custom roles for the organization
    const customRoles = await db.customRole.findMany({
      where: { organizationId: user.organizationId },
      include: {
        permissions: {
          include: {
            permission: {
              select: {
                name: true,
                displayName: true,
                description: true,
                category: true,
              },
            },
          },
        },
        userRoles: {
          select: {
            userId: true,
            user: {
              select: {
                name: true,
                email: true,
              },
            },
          },
        },
        parentRole: {
          select: {
            id: true,
            name: true,
            description: true,
          },
        },
        childRoles: {
          select: {
            id: true,
            name: true,
            description: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    // Transform custom roles
    const transformedCustomRoles = customRoles.map(role => ({
      id: role.id,
      name: role.name,
      displayName: role.description || role.name,
      description: role.description,
      isSystemRole: role.isSystemRole,
      roleType: 'custom' as const,
      parentRole: role.parentRole ? {
        ...role.parentRole,
        displayName: role.parentRole.description || role.parentRole.name,
      } : null,
      childRoles: role.childRoles.map(child => ({
        ...child,
        displayName: child.description || child.name,
      })),
      permissions: role.permissions.map(rp => rp.permission),
      userCount: role.userRoles.length,
      users: role.userRoles.map(ur => ur.user),
      createdAt: role.createdAt,
      updatedAt: role.updatedAt,
      canEdit: !role.isSystemRole,
      canDelete: !role.isSystemRole && role.userRoles.length === 0,
    }));

    // Transform system roles
    const transformedSystemRoles = Object.values(SYSTEM_ROLES).map(role => {
      const permissions = ALL_PERMISSIONS.filter(p => role.permissions.includes(p.name));
      return {
        id: `system-${role.name}`,
        name: role.name,
        displayName: role.displayName,
        description: role.description,
        isSystemRole: true,
        roleType: 'system' as const,
        parentRole: role.parentRole ? {
          id: `system-${role.parentRole}`,
          name: role.parentRole,
          displayName: SYSTEM_ROLES[role.parentRole]?.displayName || role.parentRole,
        } : null,
        childRoles: [],
        permissions: permissions,
        userCount: 0, // System roles don't have direct user assignments in this context
        users: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        canEdit: false,
        canDelete: false,
      };
    });

    // Transform predefined role templates
    const transformedPredefinedRoles = Object.values(PREDEFINED_CUSTOM_ROLES).map(role => {
      const permissions = ALL_PERMISSIONS.filter(p => role.permissions.includes(p.name));
      return {
        id: `predefined-${role.name}`,
        name: role.name,
        displayName: role.displayName,
        description: role.description,
        isSystemRole: false,
        roleType: 'predefined' as const,
        parentRole: null,
        childRoles: [],
        permissions: permissions,
        userCount: 0, // Templates don't have users assigned
        users: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        canEdit: false,
        canDelete: false,
        canInstantiate: true, // Can be used as template to create custom role
      };
    });

    // Combine all roles
    const allRoles = [
      ...transformedSystemRoles,
      ...transformedPredefinedRoles,
      ...transformedCustomRoles,
    ];

    return NextResponse.json({
      success: true,
      data: allRoles,
      meta: {
        systemRoles: transformedSystemRoles.length,
        predefinedRoles: transformedPredefinedRoles.length,
        customRoles: transformedCustomRoles.length,
        totalRoles: allRoles.length,
      },
    });
  } catch (error) {
    logger.error('Error fetching roles:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/rbac/roles
 * Create a new custom role
 */
export async function POST(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's organization
    const user = await db.user.findUnique({
      where: { id: session.user.id },
      select: { organizationId: true },
    });

    if (!user?.organizationId) {
      return NextResponse.json({ error: 'User not associated with organization' }, { status: 400 });
    }

    // Check permission
    const context: PermissionContext = {
      userId: session.user.id,
      organizationId: user.organizationId,
    };

    const canCreate = await hasPermission('create:role', context);
    if (!canCreate) {
      return NextResponse.json({ error: 'Permission denied' }, { status: 403 });
    }

    const body = await req.json();
    const validatedData = createRoleSchema.parse(body);

    // Check if role name already exists in the organization
    const existingRole = await db.customRole.findFirst({
      where: {
        name: validatedData.name,
        organizationId: user.organizationId,
      },
    });

    if (existingRole) {
      return NextResponse.json(
        { error: 'Role name already exists' },
        { status: 400 }
      );
    }

    // Validate permissions exist
    if (validatedData.permissions.length > 0) {
      const existingPermissions = await db.permission.findMany({
        where: {
          name: { in: validatedData.permissions },
        },
        select: { name: true },
      });

      const existingPermissionNames = existingPermissions.map(p => p.name);
      const invalidPermissions = validatedData.permissions.filter(
        p => !existingPermissionNames.includes(p)
      );

      if (invalidPermissions.length > 0) {
        return NextResponse.json(
          { error: `Invalid permissions: ${invalidPermissions.join(', ')}` },
          { status: 400 }
        );
      }
    }

    // Create role in transaction
    const result = await db.$transaction(async (tx) => {
      // Create the role
      const newRole = await tx.customRole.create({
        data: {
          name: validatedData.name,
          description: validatedData.description,
          organizationId: user.organizationId!,
          parentRoleId: validatedData.parentRoleId,
        },
      });

      // Add permissions if provided
      if (validatedData.permissions.length > 0) {
        const permissions = await tx.permission.findMany({
          where: { name: { in: validatedData.permissions } },
          select: { id: true },
        });

        await tx.rolePermission.createMany({
          data: permissions.map(permission => ({
            roleId: newRole.id,
            permissionId: permission.id,
          })),
        });
      }

      return newRole;
    });

    logger.info('Role created successfully', {
      roleId: result.id,
      roleName: result.name,
      organizationId: user.organizationId,
      createdBy: session.user.id,
    });

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    logger.error('Error creating role:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
