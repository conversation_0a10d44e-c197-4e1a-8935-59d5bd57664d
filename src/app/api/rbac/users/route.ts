import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { z } from 'zod';
import { logger } from '@/lib/logger';
import { hasPermission } from '@/lib/rbac/rbac-service';
import { PermissionContext } from '@/lib/rbac/types';
import bcrypt from 'bcryptjs';

// Schema for user creation
const createUserSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  jobTitle: z.string().optional(),
  departmentName: z.string().optional(),
  phoneNumber: z.string().optional(),
  roleIds: z.array(z.string()).default([]),
});

// Schema for user update
const updateUserSchema = createUserSchema.partial().omit({ password: true });

/**
 * GET /api/rbac/users
 * Get all users in the organization with their roles
 */
export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's organization
    const currentUser = await db.user.findUnique({
      where: { id: session.user.id },
      select: { organizationId: true },
    });

    if (!currentUser?.organizationId) {
      return NextResponse.json({ error: 'User not associated with organization' }, { status: 400 });
    }

    // Check permission
    const context: PermissionContext = {
      userId: session.user.id,
      organizationId: currentUser.organizationId,
    };

    const canRead = await hasPermission('read:user', context);
    if (!canRead) {
      return NextResponse.json({ error: 'Permission denied' }, { status: 403 });
    }

    // Parse query parameters
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search') || '';
    const roleFilter = searchParams.get('role') || '';
    const statusFilter = searchParams.get('status') || '';

    const skip = (page - 1) * limit;

    // Build where clause
    const whereClause: any = {
      organizationId: currentUser.organizationId,
    };

    if (search) {
      whereClause.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
        { jobTitle: { contains: search, mode: 'insensitive' } },
        { departmentName: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (roleFilter) {
      whereClause.customRoles = {
        some: {
          role: {
            name: roleFilter,
          },
        },
      };
    }

    // Get users with their roles
    const [users, totalCount] = await Promise.all([
      db.user.findMany({
        where: whereClause,
        include: {
          customRoles: {
            include: {
              role: {
                select: {
                  id: true,
                  name: true,
                  description: true,
                  isSystemRole: true,
                },
              },
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      }),
      db.user.count({ where: whereClause }),
    ]);

    // Transform the data
    const transformedUsers = users.map(user => ({
      id: user.id,
      name: user.name,
      email: user.email,
      jobTitle: user.jobTitle,
      departmentName: user.departmentName,
      phoneNumber: user.phoneNumber,
      systemRole: user.role,
      customRoles: user.customRoles.map(ur => ({
        ...ur.role,
        displayName: ur.role.description || ur.role.name, // Add displayName fallback
      })),
      lastLoginAt: user.lastLoginAt,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    }));

    return NextResponse.json({
      success: true,
      data: {
        users: transformedUsers,
        pagination: {
          page,
          limit,
          total: totalCount,
          pages: Math.ceil(totalCount / limit),
        },
      },
    });
  } catch (error) {
    logger.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/rbac/users
 * Create a new user in the organization
 */
export async function POST(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's organization
    const currentUser = await db.user.findUnique({
      where: { id: session.user.id },
      select: { organizationId: true },
    });

    if (!currentUser?.organizationId) {
      return NextResponse.json({ error: 'User not associated with organization' }, { status: 400 });
    }

    // Check permission
    const context: PermissionContext = {
      userId: session.user.id,
      organizationId: currentUser.organizationId,
    };

    const canCreate = await hasPermission('invite:user', context);
    if (!canCreate) {
      return NextResponse.json({ error: 'Permission denied' }, { status: 403 });
    }

    const body = await req.json();
    const validatedData = createUserSchema.parse(body);

    // Check if email already exists
    const existingUser = await db.user.findUnique({
      where: { email: validatedData.email },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 400 }
      );
    }

    // Validate roles if provided
    if (validatedData.roleIds.length > 0) {
      const roles = await db.customRole.findMany({
        where: {
          id: { in: validatedData.roleIds },
          organizationId: currentUser.organizationId,
        },
      });

      if (roles.length !== validatedData.roleIds.length) {
        const foundRoleIds = roles.map(r => r.id);
        const missingRoleIds = validatedData.roleIds.filter(id => !foundRoleIds.includes(id));
        return NextResponse.json(
          { error: `Invalid roles: ${missingRoleIds.join(', ')}` },
          { status: 400 }
        );
      }
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(validatedData.password, 12);

    // Create user in transaction
    const result = await db.$transaction(async (tx) => {
      // Create the user
      const newUser = await tx.user.create({
        data: {
          name: validatedData.name,
          email: validatedData.email,
          password: hashedPassword,
          jobTitle: validatedData.jobTitle,
          departmentName: validatedData.departmentName,
          phoneNumber: validatedData.phoneNumber,
          organizationId: currentUser.organizationId!,
          role: 'ORGANIZATION_USER',
        },
      });

      // Assign roles if provided
      if (validatedData.roleIds.length > 0) {
        await tx.userCustomRole.createMany({
          data: validatedData.roleIds.map(roleId => ({
            userId: newUser.id,
            roleId,
          })),
        });
      }

      return newUser;
    });

    logger.info('User created successfully', {
      userId: result.id,
      userEmail: result.email,
      organizationId: currentUser.organizationId,
      createdBy: session.user.id,
    });

    // Return user without password
    const { password, ...userWithoutPassword } = result;

    return NextResponse.json({
      success: true,
      data: userWithoutPassword,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    logger.error('Error creating user:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
