import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { db } from "@/lib/db";
import { uploadFile, generateUniqueFileName } from "@/lib/storage/index";
import path from "path";

/**
 * Upload a file
 *
 * @returns The uploaded file URL
 */
async function uploadFileHandler(req: NextRequest) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to upload files",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  try {
    const formData = await req.formData();
    const file = formData.get("file") as File;
    const folder = formData.get("folder") as string || "documents";
    const fileName = formData.get("fileName") as string;

    if (!file) {
      throw new ApiError(
        "No file provided",
        ErrorType.VALIDATION,
        400
      );
    }

    // Validate file size (10MB limit)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      throw new ApiError(
        "File size must be less than 10MB",
        ErrorType.VALIDATION,
        400
      );
    }

    // Validate file type
    const allowedTypes = [
      'application/pdf',
      'image/jpeg',
      'image/jpg',
      'image/png',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ];

    if (!allowedTypes.includes(file.type)) {
      throw new ApiError(
        "File type not supported. Please upload PDF, DOC, DOCX, or image files.",
        ErrorType.VALIDATION,
        400
      );
    }

    // Generate a unique filename
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const fileExtension = path.extname(file.name);
    const uniqueFileName = fileName || `${folder}/${timestamp}-${randomString}${fileExtension}`;

    // Convert file to buffer
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // Upload the file using our storage system
    const uploadResult = await uploadFile(
      buffer,
      uniqueFileName,
      file.type,
      {
        originalName: file.name,
        uploadedBy: session.user.id,
        folder: folder,
      }
    );

    if (!uploadResult.success) {
      throw new ApiError(
        uploadResult.error || "Failed to upload file",
        ErrorType.INTERNAL,
        500
      );
    }

    // Get the URL for the file
    const fileUrl = uploadResult.url;

    logger.info(`File uploaded: ${fileUrl} by user ${session.user.id}`);

    return NextResponse.json({
      url: fileUrl,
      key: uniqueFileName,
      size: file.size,
      mimeType: file.type,
      message: "File uploaded successfully",
    });
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }

    logger.error("Error uploading file:", error);
    throw new ApiError(
      "Failed to upload file",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const POST = withErrorHandling(uploadFileHandler);
