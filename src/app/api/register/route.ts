import { NextResponse } from "next/server";
import { hash } from "bcryptjs";
import { z } from "zod";
import { db } from "@/lib/db";
import { generateEmailOTP, sendOTPEmail } from "@/lib/otp";
import { logger } from "@/lib/logger";

const registerSchema = z.object({
  name: z.string().min(2),
  email: z.string().email(),
  password: z.string().min(8),
});

export async function POST(req: Request) {
  try {
    // Log request details for debugging
    console.log("Register API - Request details:", {
      method: req.method,
      url: req.url,
      headers: {
        contentType: req.headers.get("content-type"),
        contentLength: req.headers.get("content-length"),
        userAgent: req.headers.get("user-agent"),
        referer: req.headers.get("referer"),
        origin: req.headers.get("origin"),
      },
    });

    // Check if the request has a body
    if (req.headers.get("content-length") === "2" || req.headers.get("content-length") === "0") {
      console.log("Register API - Empty request detected");
      return NextResponse.json(
        { error: "Empty request body. Please provide name, email, and password." },
        { status: 400 }
      );
    }

    // Try to parse the request body
    let body;
    try {
      body = await req.json();
      console.log("Register API - Request body:", body);

      // Check if body is empty or missing required fields
      if (!body || Object.keys(body).length === 0) {
        console.log("Register API - Empty JSON body");
        return NextResponse.json(
          { error: "Empty request body. Please provide name, email, and password." },
          { status: 400 }
        );
      }
    } catch (error) {
      console.error("Register API - Error parsing request body:", error);
      return NextResponse.json(
        { error: "Invalid request body. Please provide a valid JSON body." },
        { status: 400 }
      );
    }

    // Validate the request body
    try {
      const { name, email, password } = registerSchema.parse(body);
    } catch (error) {
      console.error("Register API - Validation error:", error);
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: error.errors.map((e) => e.message).join(", ") },
          { status: 400 }
        );
      }
      throw error;
    }

    // If we get here, validation passed
    const { name, email, password } = registerSchema.parse(body);

    // Check if user already exists
    const existingUser = await db.user.findUnique({
      where: {
        email,
      },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: "User with this email already exists" },
        { status: 409 }
      );
    }

    // Hash the password
    const hashedPassword = await hash(password, 10);

    // Create the user
    const user = await db.user.create({
      data: {
        name,
        email,
        password: hashedPassword,
        emailVerified: null, // Email not verified yet
        // Default role for new users - using Prisma enum value
        role: "ORGANIZATION_USER",
      },
    });

    // Generate and send OTP for email verification
    const otp = await generateEmailOTP(email, user.id);
    await sendOTPEmail(email, name, otp);

    // Log successful registration
    logger.info(`User registered: ${email}`);

    // Return the user without the password
    const { password: _, ...userWithoutPassword } = user;

    return NextResponse.json(
      {
        user: userWithoutPassword,
        message: "User created successfully. Please check your email for the verification code.",
        requiresOnboarding: true,
        requiresOtpVerification: true
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error during registration:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map((e) => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred during registration" },
      { status: 500 }
    );
  }
}
