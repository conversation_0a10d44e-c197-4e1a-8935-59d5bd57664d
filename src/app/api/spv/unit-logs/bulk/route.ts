import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { UserRole } from "@/lib/authorization";
import { determineVerificationStatus } from "@/lib/utils/verification-status";
import { z } from "zod";

// Validation schema for unit log creation
const unitLogCreationSchema = z.object({
  projectId: z.string().min(1, "Project ID is required"),
  logDate: z.string().datetime("Invalid date format"),
  frequency: z.enum(["REAL_TIME", "HOURLY", "DAILY", "WEEKLY", "MONTHLY", "QUARTERLY", "YEARLY"]),
  unitType: z.string().min(1, "Unit type is required"),
  quantity: z.number().positive("Quantity must be positive"),
  dataSource: z.enum(["MANUAL", "CSV_UPLOAD", "API_INTEGRATION", "IOT_DEVICE"]).default("MANUAL"),
  sourceFile: z.string().optional(),
  apiSource: z.string().optional(),
  notes: z.string().optional(),
  metadata: z.record(z.any()).optional(),
  attachments: z.array(z.object({
    name: z.string(),
    url: z.string().url(),
    type: z.string(),
    size: z.number().optional(),
  })).optional(),
});

// Validation schema for bulk unit log creation
const bulkUnitLogCreationSchema = z.object({
  entries: z.array(unitLogCreationSchema).min(1).max(10000), // Limit to 10000 entries per batch
  validateOnly: z.boolean().default(false),
});

/**
 * POST /api/spv/unit-logs/bulk
 * Create multiple unit log entries for SPV user's assigned projects
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.SPV_USER) {
      return NextResponse.json(
        { error: "Access denied. SPV user role required." },
        { status: 403 }
      );
    }

    // Get SPV user details
    const spvUser = await db.sPVUser.findFirst({
      where: {
        userId: session.user.id,
        isActive: true,
      },
      include: {
        projectAssignments: {
          where: { isActive: true },
          select: { projectId: true },
        },
      },
    });

    if (!spvUser) {
      return NextResponse.json(
        { error: "SPV user not found" },
        { status: 404 }
      );
    }

    const assignedProjectIds = spvUser.projectAssignments.map(pa => pa.projectId);

    // Parse and validate request body
    const body = await request.json();
    const validatedData = bulkUnitLogCreationSchema.parse(body);

    // Validate that all projects in the entries are assigned to the SPV user
    const invalidProjects = validatedData.entries.filter(
      entry => !assignedProjectIds.includes(entry.projectId)
    );

    if (invalidProjects.length > 0) {
      return NextResponse.json(
        { 
          error: "Access denied. Some projects are not assigned to you.",
          invalidProjects: invalidProjects.map(p => p.projectId)
        },
        { status: 403 }
      );
    }

    // If validation only, return success without creating entries
    if (validatedData.validateOnly) {
      return NextResponse.json({
        success: true,
        message: "Validation successful",
        entriesCount: validatedData.entries.length,
      });
    }

    // Determine verification status for the user (same for all entries)
    const verificationStatus = await determineVerificationStatus(session.user.id, validatedData.entries[0].projectId);

    // Create unit logs in a transaction
    const result = await db.$transaction(async (tx) => {
      const createdEntries = [];

      for (const entry of validatedData.entries) {
        const unitLog = await tx.unitLog.create({
          data: {
            projectId: entry.projectId,
            logDate: new Date(entry.logDate),
            frequency: entry.frequency,
            unitType: entry.unitType,
            quantity: entry.quantity,
            dataSource: entry.dataSource as any,
            sourceFile: entry.sourceFile,
            apiSource: entry.apiSource,
            notes: entry.notes,
            metadata: entry.metadata,
            verificationStatus: verificationStatus,
            loggerId: session.user.id,
          },
          include: {
            project: {
              select: {
                id: true,
                name: true,
                type: true,
              },
            },
            logger: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        });

        createdEntries.push(unitLog);
      }

      return createdEntries;
    });

    logger.info("Bulk unit logs created via SPV", {
      spvUserId: spvUser.id,
      userId: session.user.id,
      entriesCount: result.length,
      projectIds: [...new Set(validatedData.entries.map(e => e.projectId))],
    });

    return NextResponse.json({
      success: true,
      message: `Successfully created ${result.length} unit log entries`,
      data: {
        unitLogs: result,
        entriesCount: result.length,
      },
      summary: {
        totalCreated: result.length,
        totalProcessed: validatedData.entries.length,
      },
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: "Validation failed", 
          details: error.errors 
        },
        { status: 400 }
      );
    }

    logger.error("Error creating bulk unit logs via SPV:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
