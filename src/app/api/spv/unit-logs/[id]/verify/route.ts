import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { UserRole } from "@/lib/authorization";
import { z } from "zod";

// Validation schema for verification action
const verifySchema = z.object({
  action: z.enum(["APPROVE", "REJECT"]),
  comments: z.string().optional(),
  priority: z.enum(["LOW", "MEDIUM", "HIGH"]).optional(),
});

/**
 * POST /api/spv/unit-logs/[id]/verify
 * Verify (approve/reject) unit log entry
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.SPV_USER) {
      return NextResponse.json(
        { error: "Access denied. SPV user role required." },
        { status: 403 }
      );
    }

    const unitLogId = params.id;

    // Get SPV user details
    const spvUser = await db.sPVUser.findFirst({
      where: {
        userId: session.user.id,
        isActive: true,
      },
      include: {
        projectAssignments: {
          where: { isActive: true },
          select: { projectId: true },
        },
      },
    });

    if (!spvUser) {
      return NextResponse.json(
        { error: "SPV user not found" },
        { status: 404 }
      );
    }

    const assignedProjectIds = spvUser.projectAssignments.map(pa => pa.projectId);

    // Get existing unit log
    const existingUnitLog = await db.unitLog.findUnique({
      where: { id: unitLogId },
      select: {
        id: true,
        projectId: true,
        loggerId: true,
        verificationStatus: true,
        project: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!existingUnitLog) {
      return NextResponse.json(
        { error: "Unit log not found" },
        { status: 404 }
      );
    }

    // Check access permissions
    if (!assignedProjectIds.includes(existingUnitLog.projectId)) {
      return NextResponse.json(
        { error: "Access denied. Project not assigned to you." },
        { status: 403 }
      );
    }

    const body = await request.json();
    const validatedData = verifySchema.parse(body);

    // Check role-based permissions and valid status transitions
    let canPerformAction = false;
    let newStatus = "";
    let fromStatus = existingUnitLog.verificationStatus;

    if (spvUser.role === "PROJECT_MANAGER") {
      if (existingUnitLog.verificationStatus === "SUBMITTED_FOR_VERIFICATION") {
        canPerformAction = true;
        newStatus = validatedData.action === "APPROVE" ? "VERIFIED" : "REJECTED";
      }
    } else if (spvUser.role === "SPV_ADMIN") {
      if (existingUnitLog.verificationStatus === "VERIFIED") {
        canPerformAction = true;
        newStatus = validatedData.action === "APPROVE" ? "SPV_APPROVED" : "SPV_REJECTED";
      } else if (existingUnitLog.verificationStatus === "SUBMITTED_FOR_VERIFICATION") {
        // SPV Admin can also verify directly
        canPerformAction = true;
        newStatus = validatedData.action === "APPROVE" ? "VERIFIED" : "REJECTED";
      }
    }

    if (!canPerformAction) {
      return NextResponse.json(
        { error: "Access denied. You don't have permission to perform this action on this unit log in its current status." },
        { status: 403 }
      );
    }

    // For rejections, comments should be provided
    if (validatedData.action === "REJECT" && !validatedData.comments?.trim()) {
      return NextResponse.json(
        { error: "Comments are required when rejecting an entry." },
        { status: 400 }
      );
    }

    // Update unit log status and create verification log
    const result = await db.$transaction(async (tx) => {
      // Update unit log status
      const updatedUnitLog = await tx.unitLog.update({
        where: { id: unitLogId },
        data: {
          verificationStatus: newStatus,
          verifierId: session.user.id,
          verifiedAt: validatedData.action === "APPROVE" ? new Date() : null,
          verificationNotes: validatedData.comments,
          updatedAt: new Date(),
        },
        include: {
          project: {
            select: {
              id: true,
              name: true,
              type: true,
              status: true,
            },
          },
          logger: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          verifier: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      // Create verification log entry
      const verificationLog = await tx.dataVerificationLog.create({
        data: {
          unitLogId: unitLogId,
          verifierId: session.user.id,
          fromStatus: fromStatus,
          toStatus: newStatus,
          verificationNotes: validatedData.comments,
          priority: validatedData.priority || "MEDIUM",
          metadata: {
            action: validatedData.action,
            verifiedBy: session.user.id,
            verifiedAt: new Date().toISOString(),
            userRole: spvUser.role,
          },
        },
      });

      return { updatedUnitLog, verificationLog };
    });

    const actionText = validatedData.action === "APPROVE" ? "approved" : "rejected";
    
    logger.info(`Unit log ${actionText}`, {
      userId: session.user.id,
      spvUserId: spvUser.id,
      unitLogId: unitLogId,
      projectId: existingUnitLog.projectId,
      projectName: existingUnitLog.project.name,
      fromStatus: fromStatus,
      toStatus: newStatus,
      action: validatedData.action,
      userRole: spvUser.role,
    });

    return NextResponse.json({
      success: true,
      data: result.updatedUnitLog,
      message: `Unit log ${actionText} successfully`,
    });

  } catch (error) {
    logger.error("Error verifying unit log:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
