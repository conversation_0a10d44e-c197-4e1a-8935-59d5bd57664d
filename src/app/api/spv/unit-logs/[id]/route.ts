import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { UserRole } from "@/lib/authorization";
import { z } from "zod";

// Validation schema for unit log update
const unitLogUpdateSchema = z.object({
  logDate: z.string().optional(),
  unitType: z.string().optional(),
  quantity: z.number().positive().optional(),
  dataSource: z.string().optional(),
  notes: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

/**
 * GET /api/spv/unit-logs/[id]
 * Get specific unit log details
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.SPV_USER) {
      return NextResponse.json(
        { error: "Access denied. SPV user role required." },
        { status: 403 }
      );
    }

    const unitLogId = params.id;

    // Get SPV user details
    const spvUser = await db.sPVUser.findFirst({
      where: {
        userId: session.user.id,
        isActive: true,
      },
      include: {
        projectAssignments: {
          where: { isActive: true },
          select: { projectId: true },
        },
      },
    });

    if (!spvUser) {
      return NextResponse.json(
        { error: "SPV user not found" },
        { status: 404 }
      );
    }

    const assignedProjectIds = spvUser.projectAssignments.map(pa => pa.projectId);

    // Get unit log details
    const unitLog = await db.unitLog.findUnique({
      where: { id: unitLogId },
      include: {
        project: {
          select: {
            id: true,
            name: true,
            type: true,
            status: true,
            organization: {
              select: {
                id: true,
                name: true,
              },
            },
            spv: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        logger: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        verifier: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        verificationLogs: {
          include: {
            verifier: {
              select: {
                id: true,
                name: true,
                email: true,
                role: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
        documents: {
          select: {
            id: true,
            name: true,
            type: true,
            url: true,
            size: true,
            status: true,
            createdAt: true,
          },
        },
      },
    });

    if (!unitLog) {
      return NextResponse.json(
        { error: "Unit log not found" },
        { status: 404 }
      );
    }

    // Check if user has access to this unit log's project
    if (!assignedProjectIds.includes(unitLog.projectId)) {
      return NextResponse.json(
        { error: "Access denied. Project not assigned to you." },
        { status: 403 }
      );
    }

    logger.info("SPV unit log details fetched", {
      userId: session.user.id,
      spvUserId: spvUser.id,
      unitLogId: unitLogId,
    });

    return NextResponse.json({
      success: true,
      data: {
        unitLog,
        spvUser: {
          id: spvUser.id,
          role: spvUser.role,
        },
      },
    });

  } catch (error) {
    logger.error("Error fetching SPV unit log details:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/spv/unit-logs/[id]
 * Update unit log (only if user owns it and it's in DRAFT or REJECTED status)
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.SPV_USER) {
      return NextResponse.json(
        { error: "Access denied. SPV user role required." },
        { status: 403 }
      );
    }

    const unitLogId = params.id;

    // Get SPV user details
    const spvUser = await db.sPVUser.findFirst({
      where: {
        userId: session.user.id,
        isActive: true,
      },
      include: {
        projectAssignments: {
          where: { isActive: true },
          select: { projectId: true },
        },
      },
    });

    if (!spvUser) {
      return NextResponse.json(
        { error: "SPV user not found" },
        { status: 404 }
      );
    }

    const assignedProjectIds = spvUser.projectAssignments.map(pa => pa.projectId);

    // Get existing unit log
    const existingUnitLog = await db.unitLog.findUnique({
      where: { id: unitLogId },
      select: {
        id: true,
        projectId: true,
        loggerId: true,
        verificationStatus: true,
      },
    });

    if (!existingUnitLog) {
      return NextResponse.json(
        { error: "Unit log not found" },
        { status: 404 }
      );
    }

    // Check access permissions
    if (!assignedProjectIds.includes(existingUnitLog.projectId)) {
      return NextResponse.json(
        { error: "Access denied. Project not assigned to you." },
        { status: 403 }
      );
    }

    // Check ownership (only owner can edit)
    if (existingUnitLog.loggerId !== session.user.id) {
      return NextResponse.json(
        { error: "Access denied. You can only edit your own entries." },
        { status: 403 }
      );
    }

    // Check if unit log can be edited (only DRAFT or REJECTED status)
    if (!['DRAFT', 'REJECTED', 'SPV_REJECTED', 'ORG_REJECTED'].includes(existingUnitLog.verificationStatus)) {
      return NextResponse.json(
        { error: "Cannot edit unit log in current status. Only draft or rejected entries can be modified." },
        { status: 400 }
      );
    }

    const body = await request.json();
    const validatedData = unitLogUpdateSchema.parse(body);

    // Update unit log
    const updatedUnitLog = await db.unitLog.update({
      where: { id: unitLogId },
      data: {
        ...(validatedData.logDate && { logDate: new Date(validatedData.logDate) }),
        ...(validatedData.unitType && { unitType: validatedData.unitType }),
        ...(validatedData.quantity && { quantity: validatedData.quantity }),
        ...(validatedData.dataSource && { dataSource: validatedData.dataSource }),
        ...(validatedData.notes !== undefined && { notes: validatedData.notes }),
        ...(validatedData.metadata && { metadata: validatedData.metadata }),
        updatedAt: new Date(),
      },
      include: {
        project: {
          select: {
            id: true,
            name: true,
            type: true,
            status: true,
          },
        },
        logger: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    logger.info("Unit log updated", {
      userId: session.user.id,
      spvUserId: spvUser.id,
      unitLogId: unitLogId,
      changes: Object.keys(validatedData),
    });

    return NextResponse.json({
      success: true,
      data: updatedUnitLog,
      message: "Unit log updated successfully",
    });

  } catch (error) {
    logger.error("Error updating unit log:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/spv/unit-logs/[id]
 * Delete unit log (only if user owns it and it's in DRAFT status)
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.SPV_USER) {
      return NextResponse.json(
        { error: "Access denied. SPV user role required." },
        { status: 403 }
      );
    }

    const unitLogId = params.id;

    // Get SPV user details
    const spvUser = await db.sPVUser.findFirst({
      where: {
        userId: session.user.id,
        isActive: true,
      },
      include: {
        projectAssignments: {
          where: { isActive: true },
          select: { projectId: true },
        },
      },
    });

    if (!spvUser) {
      return NextResponse.json(
        { error: "SPV user not found" },
        { status: 404 }
      );
    }

    const assignedProjectIds = spvUser.projectAssignments.map(pa => pa.projectId);

    // Get existing unit log
    const existingUnitLog = await db.unitLog.findUnique({
      where: { id: unitLogId },
      select: {
        id: true,
        projectId: true,
        loggerId: true,
        verificationStatus: true,
      },
    });

    if (!existingUnitLog) {
      return NextResponse.json(
        { error: "Unit log not found" },
        { status: 404 }
      );
    }

    // Check access permissions
    if (!assignedProjectIds.includes(existingUnitLog.projectId)) {
      return NextResponse.json(
        { error: "Access denied. Project not assigned to you." },
        { status: 403 }
      );
    }

    // Check ownership (only owner can delete)
    if (existingUnitLog.loggerId !== session.user.id) {
      return NextResponse.json(
        { error: "Access denied. You can only delete your own entries." },
        { status: 403 }
      );
    }

    // Check if unit log can be deleted (only DRAFT status)
    if (existingUnitLog.verificationStatus !== 'DRAFT') {
      return NextResponse.json(
        { error: "Cannot delete unit log. Only draft entries can be deleted." },
        { status: 400 }
      );
    }

    // Delete unit log and related data
    await db.$transaction(async (tx) => {
      // Delete related documents
      await tx.document.deleteMany({
        where: { unitLogId: unitLogId },
      });

      // Delete verification logs
      await tx.dataVerificationLog.deleteMany({
        where: { unitLogId: unitLogId },
      });

      // Delete unit log
      await tx.unitLog.delete({
        where: { id: unitLogId },
      });
    });

    logger.info("Unit log deleted", {
      userId: session.user.id,
      spvUserId: spvUser.id,
      unitLogId: unitLogId,
    });

    return NextResponse.json({
      success: true,
      message: "Unit log deleted successfully",
    });

  } catch (error) {
    logger.error("Error deleting unit log:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
