import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { UserRole } from "@/lib/authorization";

/**
 * GET /api/spv/projects
 * Get projects for SPV user:
 * - For SPV_ADMIN: Get all projects belonging to the SPV (for assignment management)
 * - For other roles: Get assigned projects only
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.SPV_USER) {
      return NextResponse.json(
        { error: "Access denied. SPV user role required." },
        { status: 403 }
      );
    }

    // Get SPV user details
    const spvUser = await db.sPVUser.findFirst({
      where: {
        userId: session.user.id,
        isActive: true,
      },
      include: {
        spv: {
          include: {
            organization: true,
          },
        },
        projectAssignments: {
          where: { isActive: true },
          include: {
            project: {
              include: {
                organization: {
                  select: {
                    id: true,
                    name: true,
                    legalName: true,
                  },
                },
                spv: {
                  select: {
                    id: true,
                    name: true,
                    purpose: true,
                  },
                },
                _count: {
                  select: {
                    unitLogs: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!spvUser) {
      return NextResponse.json(
        { error: "SPV user not found" },
        { status: 404 }
      );
    }

    // Handle SPV Admin - return all SPV projects for management
    if (spvUser.role === "SPV_ADMIN") {
      const { searchParams } = new URL(request.url);
      const page = parseInt(searchParams.get("page") || "1");
      const limit = parseInt(searchParams.get("limit") || "50");
      const search = searchParams.get("search") || "";
      const status = searchParams.get("status") || "";
      const type = searchParams.get("type") || "";
      const unassignedOnly = searchParams.get("unassignedOnly") === "true";
      const userId = searchParams.get("userId"); // For checking assignments to specific user

      const skip = (page - 1) * limit;

      // Debug logging
      logger.info("SPV Admin fetching projects", {
        spvUserId: spvUser.id,
        spvId: spvUser.spvId,
        spvName: spvUser.spv?.name,
        userId: session.user.id,
      });

      // Build where clause - only show projects for this SPV
      const where: any = {
        spvId: spvUser.spvId,
      };

      if (search) {
        where.OR = [
          {
            name: {
              contains: search,
              mode: "insensitive",
            },
          },
          {
            description: {
              contains: search,
              mode: "insensitive",
            },
          },
          {
            location: {
              contains: search,
              mode: "insensitive",
            },
          },
        ];
      }

      if (status) {
        where.status = status;
      }

      if (type) {
        where.type = type;
      }

      // Get projects with pagination
      const [projects, totalCount] = await Promise.all([
        db.project.findMany({
          where,
          include: {
            spv: {
              select: {
                id: true,
                name: true,
                status: true,
              },
            },
            projectAssignments: {
              where: {
                isActive: true,
                ...(userId && { spvUserId: userId }),
              },
              include: {
                spvUser: {
                  include: {
                    user: {
                      select: {
                        id: true,
                        name: true,
                        email: true,
                        jobTitle: true,
                      },
                    },
                  },
                },
              },
            },
            _count: {
              select: {
                projectAssignments: {
                  where: { isActive: true },
                },
              },
            },
          },
          skip,
          take: limit,
          orderBy: {
            createdAt: "desc",
          },
        }),
        db.project.count({ where }),
      ]);

      // Filter for unassigned projects if requested
      let filteredProjects = projects;
      if (unassignedOnly) {
        filteredProjects = projects.filter(project => project._count.projectAssignments === 0);
      }

      // If userId is provided, mark which projects are assigned to that user
      const projectsWithAssignmentStatus = filteredProjects.map(project => ({
        ...project,
        isAssignedToUser: userId ? project.projectAssignments.some(assignment =>
          assignment.spvUser.userId === userId
        ) : false,
        assignedUsers: project.projectAssignments.map(assignment => ({
          id: assignment.spvUser.id,
          userId: assignment.spvUser.userId,
          role: assignment.spvUser.role,
          user: assignment.spvUser.user,
          assignedAt: assignment.assignedAt,
        })),
      }));

      const totalPages = Math.ceil(totalCount / limit);

      logger.info("SPV admin projects retrieved", {
        userId: session.user.id,
        spvUserId: spvUser.id,
        spvId: spvUser.spvId,
        count: filteredProjects.length,
        totalCount,
        page,
        unassignedOnly,
        whereClause: where,
        projectsFound: projects.map(p => ({ id: p.id, name: p.name, spvId: p.spvId })),
      });

      return NextResponse.json({
        success: true,
        data: {
          projects: projectsWithAssignmentStatus,
          pagination: {
            page,
            limit,
            totalCount: unassignedOnly ? filteredProjects.length : totalCount,
            totalPages: unassignedOnly ? Math.ceil(filteredProjects.length / limit) : totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1,
          },
          spv: {
            id: spvUser.spv.id,
            name: spvUser.spv.name,
            organization: spvUser.spv.organization,
          },
          spvUser: {
            id: spvUser.id,
            role: spvUser.role,
          },
        },
      });
    }

    // For non-admin SPV users, continue with assignment-based logic

    // If no project assignments, return empty array
    if (!spvUser.projectAssignments || spvUser.projectAssignments.length === 0) {
      logger.info("SPV projects fetched", {
        userId: session.user.id,
        spvUserId: spvUser.id,
        projectCount: 0,
      });

      return NextResponse.json({
        success: true,
        data: {
          projects: [],
          spvUser: {
            id: spvUser.id,
            role: spvUser.role,
            spv: {
              id: spvUser.spv.id,
              name: spvUser.spv.name,
              purpose: spvUser.spv.purpose,
            },
          },
        },
      });
    }

    // Get additional statistics for each project
    const projectsWithStats = await Promise.all(
      spvUser.projectAssignments.map(async (assignment) => {
        try {
          const project = assignment.project;

          // Get unit log statistics for this project
          const [
            totalUnitLogs,
            draftCount,
            submittedCount,
            verifiedCount,
            approvedCount,
            rejectedCount,
            userUnitLogs
          ] = await Promise.all([
          db.unitLog.count({
            where: { projectId: project.id },
          }),
          db.unitLog.count({
            where: {
              projectId: project.id,
              verificationStatus: 'DRAFT',
            },
          }),
          db.unitLog.count({
            where: {
              projectId: project.id,
              verificationStatus: 'SUBMITTED_FOR_VERIFICATION',
            },
          }),
          db.unitLog.count({
            where: {
              projectId: project.id,
              verificationStatus: 'VERIFIED',
            },
          }),
          db.unitLog.count({
            where: {
              projectId: project.id,
              verificationStatus: {
                in: ['SPV_APPROVED', 'ORG_APPROVED', 'VVB_VERIFIED']
              },
            },
          }),
          db.unitLog.count({
            where: {
              projectId: project.id,
              verificationStatus: {
                in: ['SPV_REJECTED', 'ORG_REJECTED', 'VVB_REJECTED']
              },
            },
          }),
          db.unitLog.count({
            where: {
              projectId: project.id,
              loggedBy: session.user.id,
            },
          }),
        ]);

        return {
          id: project.id,
          name: project.name,
          description: project.description,
          type: project.type,
          status: project.status,
          location: project.location,
          estimatedReductions: project.estimatedReductions,
          createdAt: project.createdAt,
          organization: project.organization,
          spv: project.spv,
          assignment: {
            id: assignment.id,
            assignedAt: assignment.assignedAt,
            assignedBy: assignment.assignedBy,
            isActive: assignment.isActive,
          },
          statistics: {
            totalUnitLogs,
            draftCount,
            submittedCount,
            verifiedCount,
            approvedCount,
            rejectedCount,
            userUnitLogs,
            completionRate: totalUnitLogs > 0 ? Math.round((approvedCount / totalUnitLogs) * 100) : 0,
            rejectionRate: totalUnitLogs > 0 ? Math.round((rejectedCount / totalUnitLogs) * 100) : 0,
          },
        };
        } catch (error) {
          logger.error("Error processing project assignment", {
            assignmentId: assignment.id,
            projectId: assignment.project?.id,
            error: error
          });
          throw error;
        }
      })
    );

    logger.info("SPV projects fetched", {
      userId: session.user.id,
      spvUserId: spvUser.id,
      projectCount: projectsWithStats.length,
    });

    return NextResponse.json({
      success: true,
      data: {
        projects: projectsWithStats,
        spvUser: {
          id: spvUser.id,
          role: spvUser.role,
          spv: {
            id: spvUser.spv.id,
            name: spvUser.spv.name,
            purpose: spvUser.spv.purpose,
          },
        },
      },
    });

  } catch (error) {
    logger.error("Error fetching SPV projects:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
