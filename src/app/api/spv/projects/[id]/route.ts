import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { UserRole } from "@/lib/authorization";

/**
 * GET /api/spv/projects/[id]
 * Get specific project details for SPV user
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.SPV_USER) {
      return NextResponse.json(
        { error: "Access denied. SPV user role required." },
        { status: 403 }
      );
    }

    const projectId = params.id;

    // Get SPV user details
    const spvUser = await db.sPVUser.findFirst({
      where: {
        userId: session.user.id,
        isActive: true,
      },
      include: {
        projectAssignments: {
          where: { 
            isActive: true,
            projectId: projectId,
          },
        },
      },
    });

    if (!spvUser) {
      return NextResponse.json(
        { error: "SPV user not found" },
        { status: 404 }
      );
    }

    // Check if user has access to this project
    const hasAccess = spvUser.projectAssignments.some(
      assignment => assignment.projectId === projectId
    );

    if (!hasAccess) {
      return NextResponse.json(
        { error: "Access denied. Project not assigned to you." },
        { status: 403 }
      );
    }

    // Get project details
    const project = await db.project.findUnique({
      where: { id: projectId },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            legalName: true,
          },
        },
        spv: {
          select: {
            id: true,
            name: true,
            purpose: true,
            jurisdiction: true,
          },
        },
        documents: {
          select: {
            id: true,
            name: true,
            type: true,
            url: true,
            status: true,
            createdAt: true,
          },
        },
        _count: {
          select: {
            unitLogs: true,
          },
        },
      },
    });

    if (!project) {
      return NextResponse.json(
        { error: "Project not found" },
        { status: 404 }
      );
    }

    // Get unit log statistics
    const [
      totalUnitLogs,
      draftCount,
      submittedCount,
      verifiedCount,
      approvedCount,
      rejectedCount,
      userUnitLogs,
      recentUnitLogs
    ] = await Promise.all([
      db.unitLog.count({
        where: { projectId: projectId },
      }),
      db.unitLog.count({
        where: { 
          projectId: projectId,
          verificationStatus: 'DRAFT',
        },
      }),
      db.unitLog.count({
        where: { 
          projectId: projectId,
          verificationStatus: 'SUBMITTED_FOR_VERIFICATION',
        },
      }),
      db.unitLog.count({
        where: { 
          projectId: projectId,
          verificationStatus: 'VERIFIED',
        },
      }),
      db.unitLog.count({
        where: { 
          projectId: projectId,
          verificationStatus: {
            in: ['SPV_APPROVED', 'ORG_APPROVED', 'VVB_VERIFIED']
          },
        },
      }),
      db.unitLog.count({
        where: { 
          projectId: projectId,
          verificationStatus: {
            in: ['REJECTED', 'SPV_REJECTED', 'ORG_REJECTED', 'VVB_REJECTED']
          },
        },
      }),
      db.unitLog.count({
        where: { 
          projectId: projectId,
          loggerId: session.user.id,
        },
      }),
      db.unitLog.findMany({
        where: { projectId: projectId },
        include: {
          logger: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          verifier: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: 10,
      }),
    ]);

    const projectWithStats = {
      ...project,
      assignment: spvUser.projectAssignments[0],
      statistics: {
        totalUnitLogs,
        draftCount,
        submittedCount,
        verifiedCount,
        approvedCount,
        rejectedCount,
        userUnitLogs,
        completionRate: totalUnitLogs > 0 ? Math.round((approvedCount / totalUnitLogs) * 100) : 0,
        rejectionRate: totalUnitLogs > 0 ? Math.round((rejectedCount / totalUnitLogs) * 100) : 0,
      },
      recentUnitLogs,
    };

    logger.info("SPV project details fetched", {
      userId: session.user.id,
      spvUserId: spvUser.id,
      projectId: projectId,
    });

    return NextResponse.json({
      success: true,
      data: {
        project: projectWithStats,
        spvUser: {
          id: spvUser.id,
          role: spvUser.role,
        },
      },
    });

  } catch (error) {
    logger.error("Error fetching SPV project details:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
