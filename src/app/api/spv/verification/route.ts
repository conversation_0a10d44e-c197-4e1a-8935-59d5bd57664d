import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { UserRole } from "@/lib/authorization";
import { DataVerificationStatus } from "@prisma/client";
import { z } from "zod";

// Validation schema for verification action
const verificationActionSchema = z.object({
  unitLogId: z.string().min(1, "Unit log ID is required"),
  action: z.enum([
    "SUBMIT_FOR_VERIFICATION",
    "PM_VERIFY",           // Project Manager verification
    "SPV_ADMIN_VERIFY",    // SPV Admin verification
    "VERIFY",              // Final verification (org admin)
    "REJECT",              // Reject at any stage
    "SPV_APPROVE",         // Legacy - keeping for compatibility
    "SPV_REJECT",          // Legacy - keeping for compatibility
    "ORG_APPROVE",
    "ORG_REJECT",
    "SUBMIT_TO_VVB"
  ]),
  notes: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

/**
 * GET /api/spv/verification
 * Get verification queue for current SPV user
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.SPV_USER) {
      return NextResponse.json(
        { error: "Access denied. SPV user role required." },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const status = searchParams.get("status");
    const projectId = searchParams.get("projectId");

    const skip = (page - 1) * limit;

    // Get SPV user details
    const spvUser = await db.sPVUser.findFirst({
      where: {
        userId: session.user.id,
        isActive: true,
      },
      include: {
        projectAssignments: {
          where: { isActive: true },
          select: { projectId: true },
        },
      },
    });

    if (!spvUser) {
      return NextResponse.json(
        { error: "SPV user not found" },
        { status: 404 }
      );
    }

    // Get assigned project IDs
    const assignedProjectIds = spvUser.projectAssignments.map(a => a.projectId);

    if (assignedProjectIds.length === 0) {
      return NextResponse.json({
        success: true,
        data: {
          unitLogs: [],
          pagination: {
            page,
            limit,
            totalCount: 0,
            totalPages: 0,
            hasNext: false,
            hasPrev: false,
          },
        },
      });
    }

    // Build where clause
    const where: any = {
      projectId: { in: assignedProjectIds },
    };

    if (projectId) {
      where.projectId = projectId;
    }

    // Filter by verification status based on user role
    if (spvUser.role === "SITE_WORKER") {
      // Site workers see their own entries
      where.loggedBy = session.user.id;
      if (status) {
        where.verificationStatus = status;
      }
    } else if (spvUser.role === "PROJECT_MANAGER") {
      // Project managers see entries that need verification
      if (status) {
        where.verificationStatus = status;
      } else {
        where.verificationStatus = { in: ["SUBMITTED_FOR_VERIFICATION", "VERIFIED", "REJECTED"] };
      }
    } else if (spvUser.role === "SPV_ADMIN") {
      // SPV admins see entries that need SPV approval
      if (status) {
        where.verificationStatus = status;
      } else {
        where.verificationStatus = { in: ["VERIFIED", "SPV_APPROVED", "SPV_REJECTED"] };
      }
    }

    // Get unit logs with verification data
    const [unitLogs, totalCount] = await Promise.all([
      db.unitLog.findMany({
        where,
        include: {
          logger: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          verifier: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          project: {
            select: {
              id: true,
              name: true,
              type: true,
              status: true,
            },
          },
          verificationLogs: {
            include: {
              verifier: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  role: true,
                },
              },
            },
            orderBy: { createdAt: "desc" },
            take: 5,
          },
        },
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
      }),
      db.unitLog.count({ where }),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    logger.info("Verification queue retrieved", {
      userId: session.user.id,
      spvUserId: spvUser.id,
      role: spvUser.role,
      count: unitLogs.length,
      totalCount,
    });

    return NextResponse.json({
      success: true,
      data: {
        unitLogs,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
        userRole: spvUser.role,
      },
    });

  } catch (error) {
    logger.error("Error retrieving verification queue", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/spv/verification
 * Perform verification action on unit log
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.SPV_USER) {
      return NextResponse.json(
        { error: "Access denied. SPV user role required." },
        { status: 403 }
      );
    }

    const body = await request.json();
    const validatedData = verificationActionSchema.parse(body);

    // Get SPV user details
    const spvUser = await db.sPVUser.findFirst({
      where: {
        userId: session.user.id,
        isActive: true,
      },
      include: {
        projectAssignments: {
          where: { isActive: true },
          select: { projectId: true },
        },
      },
    });

    if (!spvUser) {
      return NextResponse.json(
        { error: "SPV user not found" },
        { status: 404 }
      );
    }

    // Get the unit log
    const unitLog = await db.unitLog.findUnique({
      where: { id: validatedData.unitLogId },
      include: { project: true },
    });

    if (!unitLog) {
      return NextResponse.json(
        { error: "Unit log not found" },
        { status: 404 }
      );
    }

    // Check if user has access to this project
    const hasAccess = spvUser.projectAssignments.some(
      a => a.projectId === unitLog.projectId
    );

    if (!hasAccess) {
      return NextResponse.json(
        { error: "Access denied. You don't have access to this project." },
        { status: 403 }
      );
    }

    // Validate action based on current status and user role
    const canPerformAction = validateVerificationAction(
      validatedData.action,
      unitLog.verificationStatus,
      spvUser.role,
      unitLog.loggedBy === session.user.id
    );

    if (!canPerformAction.allowed) {
      return NextResponse.json(
        { error: canPerformAction.reason },
        { status: 400 }
      );
    }

    // Perform the verification action in a transaction
    const result = await db.$transaction(async (tx) => {
      // Determine new status and update fields
      const updateData = getVerificationUpdateData(validatedData.action, session.user.id, validatedData.notes);

      // Update the unit log
      const updatedUnitLog = await tx.unitLog.update({
        where: { id: validatedData.unitLogId },
        data: updateData,
        include: {
          logger: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          verifier: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          project: {
            select: {
              id: true,
              name: true,
              type: true,
              status: true,
            },
          },
        },
      });

      // Create verification log entry
      await tx.dataVerificationLog.create({
        data: {
          unitLogId: validatedData.unitLogId,
          fromStatus: unitLog.verificationStatus,
          toStatus: updateData.verificationStatus,
          verifiedBy: session.user.id,
          verificationNotes: validatedData.notes,
          metadata: validatedData.metadata,
        },
      });

      return updatedUnitLog;
    });

    logger.info("Verification action performed", {
      userId: session.user.id,
      spvUserId: spvUser.id,
      unitLogId: validatedData.unitLogId,
      action: validatedData.action,
      fromStatus: unitLog.verificationStatus,
      toStatus: result.verificationStatus,
    });

    return NextResponse.json({
      success: true,
      data: result,
      message: `${validatedData.action.replace('_', ' ').toLowerCase()} completed successfully`,
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    logger.error("Error performing verification action", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Helper function to validate if an action can be performed
function validateVerificationAction(
  action: string,
  currentStatus: string,
  userRole: string,
  isOwner: boolean
): { allowed: boolean; reason?: string } {
  const transitions: Record<string, { allowedRoles: string[]; fromStatuses: string[]; requiresOwnership?: boolean }> = {
    SUBMIT_FOR_VERIFICATION: {
      allowedRoles: ["SITE_WORKER", "PROJECT_MANAGER", "SPV_ADMIN"],
      fromStatuses: ["DRAFT", "REJECTED"],
      requiresOwnership: true,
    },
    PM_VERIFY: {
      allowedRoles: ["PROJECT_MANAGER", "SPV_ADMIN"],
      fromStatuses: ["DRAFT", "SUBMITTED_FOR_VERIFICATION"],
    },
    SPV_ADMIN_VERIFY: {
      allowedRoles: ["SPV_ADMIN"],
      fromStatuses: ["PM_VERIFIED"],
    },
    VERIFY: {
      allowedRoles: ["PROJECT_MANAGER", "SPV_ADMIN"],
      fromStatuses: ["SPV_ADMIN_VERIFIED", "SUBMITTED_FOR_VERIFICATION"],
    },
    REJECT: {
      allowedRoles: ["PROJECT_MANAGER", "SPV_ADMIN"],
      fromStatuses: ["DRAFT", "PM_VERIFIED", "SPV_ADMIN_VERIFIED", "SUBMITTED_FOR_VERIFICATION", "VERIFIED"],
    },
    SPV_APPROVE: {
      allowedRoles: ["SPV_ADMIN"],
      fromStatuses: ["VERIFIED"],
    },
    SPV_REJECT: {
      allowedRoles: ["SPV_ADMIN"],
      fromStatuses: ["VERIFIED"],
    },
  };

  const transition = transitions[action];
  if (!transition) {
    return { allowed: false, reason: "Invalid action" };
  }

  if (!transition.allowedRoles.includes(userRole)) {
    return { allowed: false, reason: "Insufficient permissions for this action" };
  }

  if (!transition.fromStatuses.includes(currentStatus)) {
    return { allowed: false, reason: `Cannot ${action} from current status: ${currentStatus}` };
  }

  if (transition.requiresOwnership && !isOwner) {
    return { allowed: false, reason: "You can only perform this action on your own entries" };
  }

  return { allowed: true };
}

// Helper function to get update data for verification action
function getVerificationUpdateData(action: string, userId: string, notes?: string): any {
  const now = new Date();

  switch (action) {
    case "SUBMIT_FOR_VERIFICATION":
      return {
        verificationStatus: DataVerificationStatus.SUBMITTED_FOR_VERIFICATION,
      };
    case "PM_VERIFY":
      return {
        verificationStatus: DataVerificationStatus.PM_VERIFIED,
        verifiedBy: userId,
        verifiedAt: now,
        verificationNotes: notes,
      };
    case "SPV_ADMIN_VERIFY":
      return {
        verificationStatus: DataVerificationStatus.SPV_ADMIN_VERIFIED,
        spvApprovedBy: userId,
        spvApprovedAt: now,
        spvApprovalNotes: notes,
      };
    case "VERIFY":
      return {
        verificationStatus: DataVerificationStatus.VERIFIED,
        orgApprovedBy: userId,
        orgApprovedAt: now,
        orgApprovalNotes: notes,
      };
    case "REJECT":
      return {
        verificationStatus: DataVerificationStatus.REJECTED,
        verifiedBy: userId,
        verifiedAt: now,
        verificationNotes: notes,
      };
    case "SPV_APPROVE":
      return {
        verificationStatus: DataVerificationStatus.SPV_APPROVED,
        spvApprovedBy: userId,
        spvApprovedAt: now,
        spvApprovalNotes: notes,
      };
    case "SPV_REJECT":
      return {
        verificationStatus: DataVerificationStatus.SPV_REJECTED,
        spvApprovedBy: userId,
        spvApprovedAt: now,
        spvApprovalNotes: notes,
      };
    default:
      throw new Error(`Unknown action: ${action}`);
  }
}
