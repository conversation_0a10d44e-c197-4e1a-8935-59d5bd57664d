import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { UserRole } from "@/lib/authorization";

/**
 * GET /api/spv/dashboard/stats
 * Get dashboard statistics for SPV user
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.SPV_USER) {
      return NextResponse.json(
        { error: "Access denied. SPV user role required." },
        { status: 403 }
      );
    }

    // Get SPV user details
    const spvUser = await db.sPVUser.findFirst({
      where: {
        userId: session.user.id,
        isActive: true,
      },
      include: {
        projectAssignments: {
          where: { isActive: true },
          select: { projectId: true },
        },
      },
    });

    if (!spvUser) {
      return NextResponse.json(
        { error: "SPV user not found" },
        { status: 404 }
      );
    }

    const assignedProjectIds = spvUser.projectAssignments.map(pa => pa.projectId);

    // If no projects assigned, return zero stats
    if (assignedProjectIds.length === 0) {
      return NextResponse.json({
        success: true,
        data: {
          assignedProjectsCount: 0,
          pendingVerificationCount: 0,
          completedEntriesCount: 0,
          rejectedEntriesCount: 0,
          totalUnitLogs: 0,
          userUnitLogs: 0,
          completionRate: 0,
          rejectionRate: 0,
        },
      });
    }

    // Get comprehensive statistics
    const [
      assignedProjectsCount,
      pendingVerificationCount,
      completedEntriesCount,
      rejectedEntriesCount,
      totalUnitLogs,
      userUnitLogs
    ] = await Promise.all([
      // Assigned projects count
      db.project.count({
        where: {
          id: { in: assignedProjectIds },
          status: "ACTIVE",
        },
      }),

      // Pending verification count
      db.unitLog.count({
        where: {
          projectId: { in: assignedProjectIds },
          verificationStatus: {
            in: ["SUBMITTED_FOR_VERIFICATION", "VERIFIED"]
          },
        },
      }),

      // Completed entries count
      db.unitLog.count({
        where: {
          projectId: { in: assignedProjectIds },
          verificationStatus: {
            in: ["SPV_APPROVED", "ORG_APPROVED", "VVB_VERIFIED"]
          },
        },
      }),

      // Rejected entries count
      db.unitLog.count({
        where: {
          projectId: { in: assignedProjectIds },
          verificationStatus: {
            in: ["SPV_REJECTED", "ORG_REJECTED", "VVB_REJECTED"]
          },
        },
      }),

      // Total unit logs for assigned projects
      db.unitLog.count({
        where: {
          projectId: { in: assignedProjectIds },
        },
      }),

      // Unit logs created by this user
      db.unitLog.count({
        where: {
          loggedBy: session.user.id,
          projectId: { in: assignedProjectIds },
        },
      }),
    ]);

    const stats = {
      assignedProjectsCount,
      pendingVerificationCount,
      completedEntriesCount,
      rejectedEntriesCount,
      totalUnitLogs,
      userUnitLogs,
      completionRate: totalUnitLogs > 0 ? Math.round((completedEntriesCount / totalUnitLogs) * 100) : 0,
      rejectionRate: totalUnitLogs > 0 ? Math.round((rejectedEntriesCount / totalUnitLogs) * 100) : 0,
    };

    logger.info("SPV dashboard stats fetched", {
      userId: session.user.id,
      spvUserId: spvUser.id,
      stats,
    });

    return NextResponse.json({
      success: true,
      data: stats,
    });

  } catch (error) {
    logger.error("Error fetching SPV dashboard stats:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
