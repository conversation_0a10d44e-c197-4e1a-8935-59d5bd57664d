import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { z } from "zod";

// Validation schema for document verification
const documentVerificationSchema = z.object({
  verified: z.boolean(),
  notes: z.string().optional(),
});

/**
 * POST /api/organizations/spvs/[id]/documents/[documentId]/verify
 * Verify or reject a specific SPV document
 */
async function verifyDocumentHandler(
  request: NextRequest,
  { params }: { params: { id: string; documentId: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to verify documents",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    // Only platform admins can verify documents
    if (session.user.role !== "ADMIN") {
      throw new ApiError(
        "You don't have permission to verify documents",
        ErrorType.AUTHORIZATION,
        403
      );
    }

    const spvId = params.id;
    const documentId = params.documentId;
    const body = await request.json();
    const validatedData = documentVerificationSchema.parse(body);

    // Get document with SPV details
    const document = await db.sPVDocument.findUnique({
      where: { id: documentId },
      include: {
        spv: {
          select: {
            id: true,
            name: true,
            organizationId: true,
            verificationStatus: true,
          },
        },
        uploader: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!document) {
      throw new ApiError("Document not found", ErrorType.NOT_FOUND, 404);
    }

    if (document.spv.id !== spvId) {
      throw new ApiError("Document does not belong to this SPV", ErrorType.VALIDATION, 400);
    }

    // Update document verification status
    const updatedDocument = await db.sPVDocument.update({
      where: { id: documentId },
      data: {
        verified: validatedData.verified,
        verifiedBy: session.user.id,
        verifiedAt: new Date(),
        notes: validatedData.notes || null,
        updatedAt: new Date(),
      },
      include: {
        uploader: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        verifier: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    logger.info(`Document verification updated: ${documentId} - ${validatedData.verified ? 'verified' : 'rejected'} by admin ${session.user.id}`);

    // Check if all required documents are verified
    const allDocuments = await db.sPVDocument.findMany({
      where: { spvId },
      select: {
        documentType: true,
        verified: true,
      },
    });

    // Define required document types
    const requiredDocumentTypes = [
      "CERTIFICATE_OF_INCORPORATION",
      "PAN_CARD",
      "GST_REGISTRATION",
      "BOARD_RESOLUTION",
      "BANK_PROOF",
      "AUTHORIZED_SIGNATORY_ID",
      "MOA_AOA",
    ];

    const uploadedRequiredDocs = allDocuments.filter(doc => 
      requiredDocumentTypes.includes(doc.documentType)
    );

    const allRequiredDocsVerified = requiredDocumentTypes.every(requiredType => {
      const doc = uploadedRequiredDocs.find(d => d.documentType === requiredType);
      return doc && doc.verified;
    });

    const hasAllRequiredDocs = requiredDocumentTypes.every(requiredType => 
      uploadedRequiredDocs.some(d => d.documentType === requiredType)
    );

    // Update SPV verification status if all documents are ready
    let spvUpdateData: any = {};
    let statusMessage = "";

    if (hasAllRequiredDocs && allRequiredDocsVerified) {
      // All required documents are uploaded and verified
      spvUpdateData.verificationStatus = "VERIFIED";
      spvUpdateData.verifiedBy = session.user.id;
      spvUpdateData.verifiedAt = new Date();
      statusMessage = "All required documents verified. SPV is now fully verified.";
    } else if (hasAllRequiredDocs && !allRequiredDocsVerified) {
      // All documents uploaded but not all verified
      statusMessage = "Document verification updated. Waiting for remaining documents to be verified.";
    } else {
      // Missing required documents
      statusMessage = "Document verification updated. Some required documents are still missing.";
    }

    if (Object.keys(spvUpdateData).length > 0) {
      await db.sPV.update({
        where: { id: spvId },
        data: spvUpdateData,
      });
    }

    return NextResponse.json({
      document: updatedDocument,
      message: `Document ${validatedData.verified ? 'verified' : 'rejected'} successfully. ${statusMessage}`,
      spvFullyVerified: allRequiredDocsVerified && hasAllRequiredDocs,
      documentsSummary: {
        total: allDocuments.length,
        verified: allDocuments.filter(d => d.verified).length,
        requiredUploaded: uploadedRequiredDocs.length,
        requiredTotal: requiredDocumentTypes.length,
      },
    });
  } catch (error) {
    logger.error("Error verifying document:", error);

    if (error instanceof ApiError) {
      throw error;
    }

    // Handle validation errors
    if (error instanceof Error && error.name === 'ZodError') {
      throw new ApiError(
        "Invalid verification data provided",
        ErrorType.VALIDATION,
        400
      );
    }

    throw new ApiError(
      "An error occurred while verifying the document",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const POST = withErrorHandling(verifyDocumentHandler);
