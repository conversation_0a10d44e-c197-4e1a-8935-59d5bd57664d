import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { z } from "zod";

const updateUserSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email address"),
  role: z.enum(["PROJECT_MANAGER", "SITE_WORKER"], {
    required_error: "Role is required",
  }),
  jobTitle: z.string().optional(),
  phoneNumber: z.string().optional(),
});

/**
 * PUT /api/organizations/spvs/[id]/users/[userId]
 * Update an SPV user
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; userId: string }> }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== "ORGANIZATION_ADMIN" && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Access denied. Organization admin or platform admin role required." },
        { status: 403 }
      );
    }

    const { id: spvId, userId } = await params;
    const body = await request.json();
    const validatedData = updateUserSchema.parse(body);

    // Verify SPV exists and belongs to user's organization (for org admins)
    const whereClause: any = { id: spvId };
    if (session.user.role === "ORGANIZATION_ADMIN") {
      whereClause.organizationId = session.user.organizationId;
    }

    const spv = await db.sPV.findFirst({
      where: whereClause,
    });

    if (!spv) {
      return NextResponse.json(
        { error: "SPV not found or access denied" },
        { status: 404 }
      );
    }

    // Find the SPV user
    const spvUser = await db.sPVUser.findFirst({
      where: {
        id: userId,
        spvId: spv.id,
      },
      include: {
        user: true,
      },
    });

    if (!spvUser) {
      return NextResponse.json(
        { error: "User not found in this SPV" },
        { status: 404 }
      );
    }

    // Check if email is being changed and if it's already in use
    if (validatedData.email !== spvUser.user.email) {
      const existingUser = await db.user.findFirst({
        where: {
          email: validatedData.email,
          id: { not: spvUser.user.id },
        },
      });

      if (existingUser) {
        return NextResponse.json(
          { error: "Email address is already in use" },
          { status: 400 }
        );
      }
    }

    // Update the user and SPV user in a transaction
    const result = await db.$transaction(async (tx) => {
      // Update the user record
      const updatedUser = await tx.user.update({
        where: { id: spvUser.user.id },
        data: {
          name: validatedData.name,
          email: validatedData.email,
          jobTitle: validatedData.jobTitle,
          phoneNumber: validatedData.phoneNumber,
        },
      });

      // Update the SPV user record
      const updatedSPVUser = await tx.sPVUser.update({
        where: { id: userId },
        data: {
          role: validatedData.role,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              jobTitle: true,
              phoneNumber: true,
            },
          },
        },
      });

      return { user: updatedUser, spvUser: updatedSPVUser };
    });

    logger.info("SPV user updated successfully", {
      userId: session.user.id,
      spvId: spv.id,
      updatedUserId: spvUser.user.id,
      role: validatedData.role,
    });

    return NextResponse.json({
      message: "User updated successfully",
      user: result.spvUser,
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid input data", details: error.errors },
        { status: 400 }
      );
    }

    logger.error("Error updating SPV user:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/organizations/spvs/[id]/users/[userId]
 * Remove a user from the SPV
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; userId: string }> }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== "ORGANIZATION_ADMIN" && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Access denied. Organization admin or platform admin role required." },
        { status: 403 }
      );
    }

    const { id: spvId, userId } = await params;

    // Verify SPV exists and belongs to user's organization (for org admins)
    const whereClause: any = { id: spvId };
    if (session.user.role === "ORGANIZATION_ADMIN") {
      whereClause.organizationId = session.user.organizationId;
    }

    const spv = await db.sPV.findFirst({
      where: whereClause,
    });

    if (!spv) {
      return NextResponse.json(
        { error: "SPV not found or access denied" },
        { status: 404 }
      );
    }

    // Find and delete the SPV user
    const spvUser = await db.sPVUser.findFirst({
      where: {
        id: userId,
        spvId: spv.id,
      },
      include: {
        user: true,
      },
    });

    if (!spvUser) {
      return NextResponse.json(
        { error: "User not found in this SPV" },
        { status: 404 }
      );
    }

    // Remove the SPV user (this will also handle project assignments via cascade)
    await db.sPVUser.delete({
      where: { id: userId },
    });

    logger.info("SPV user removed successfully", {
      userId: session.user.id,
      spvId: spv.id,
      removedUserId: spvUser.user.id,
    });

    return NextResponse.json({
      message: "User removed from SPV successfully",
    });

  } catch (error) {
    logger.error("Error removing SPV user:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
