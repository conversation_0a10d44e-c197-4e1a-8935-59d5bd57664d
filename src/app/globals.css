@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter), var(--font-geist), ui-sans-serif, system-ui, sans-serif;
  --font-mono: var(--font-geist-mono), ui-monospace, SFMono-Regular, "SF Mono", Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;

  @keyframes accordion-down {
    from {
      height: 0;
    }

    to {
      height: var(--radix-accordion-content-height);
    }
  }

  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }

    to {
      height: 0;
    }
  }
}

:root {
  --radius: 0.625rem;
  /* Green theme background - light green similar to the reference website */
  --background: oklch(0.98 0.02 142);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  /* Green primary color */
  --primary: oklch(0.45 0.15 142);
  --primary-foreground: oklch(0.985 0 0);
  /* Light green secondary */
  --secondary: oklch(0.95 0.03 142);
  --secondary-foreground: oklch(0.205 0 0);
  /* Muted green tones */
  --muted: oklch(0.96 0.02 142);
  --muted-foreground: oklch(0.556 0 0);
  /* Green accent */
  --accent: oklch(0.94 0.04 142);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  /* Green-tinted borders */
  --border: oklch(0.92 0.01 142);
  --input: oklch(0.92 0.01 142);
  --ring: oklch(0.45 0.15 142);
  /* Green chart colors */
  --chart-1: oklch(0.55 0.15 142);
  --chart-2: oklch(0.6 0.12 160);
  --chart-3: oklch(0.4 0.1 120);
  --chart-4: oklch(0.7 0.18 140);
  --chart-5: oklch(0.65 0.16 150);
  /* Green sidebar theme */
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.45 0.15 142);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.94 0.04 142);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.92 0.01 142);
  --sidebar-ring: oklch(0.45 0.15 142);

  /* Animation variables */
  --animation-multiplier: 1;
  --animation-duration-fast: calc(0.15s * var(--animation-multiplier));
  --animation-duration-medium: calc(0.3s * var(--animation-multiplier));
  --animation-duration-slow: calc(0.5s * var(--animation-multiplier));
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }

  /* Updated Typography Hierarchy - Reduced Bold Usage */
  h1 {
    @apply text-[28px] font-bold leading-tight tracking-tight text-foreground;
  }

  h2 {
    @apply text-[20px] font-medium leading-tight text-foreground;
  }

  h3 {
    @apply text-[16px] font-medium leading-snug text-foreground;
  }

  h4 {
    @apply text-sm font-medium leading-snug text-foreground;
  }

  h5 {
    @apply text-xs font-medium leading-normal text-foreground;
  }

  h6 {
    @apply text-xs font-normal leading-normal text-foreground;
  }

  /* Body text with normal weight */
  p {
    @apply text-sm font-normal leading-relaxed text-foreground;
  }

  /* Utility text sizes */
  .text-large {
    @apply text-base leading-relaxed;
  }

  .text-small {
    @apply text-xs leading-relaxed;
  }

  .text-caption {
    @apply text-xs leading-normal text-muted-foreground;
  }

  /* Compact Spacing Scale for MVP */
  .space-xs { @apply space-y-1; }      /* 4px */
  .space-sm { @apply space-y-2; }      /* 8px */
  .space-md { @apply space-y-3; }      /* 12px */
  .space-lg { @apply space-y-4; }      /* 16px */
  .space-xl { @apply space-y-6; }      /* 24px */

  /* Compact Card spacing utilities */
  .card-padding { @apply p-4; }        /* 16px - default */
  .card-padding-sm { @apply p-3; }     /* 12px - compact */
  .card-padding-lg { @apply p-6; }     /* 24px - spacious */

  /* Card header specific spacing */
  .card-header-padding { @apply px-4 pt-4 pb-2; }
  .card-content-padding { @apply px-4 pb-4; }
  .card-footer-padding { @apply px-4 pb-4 pt-2; }

  /* Card spacing variants */
  .card-compact { @apply p-3 space-y-2; }
  .card-comfortable { @apply p-4 space-y-3; }
  .card-spacious { @apply p-6 space-y-4; }

  /* Form spacing utilities */
  .form-spacing { @apply space-y-4; }  /* 16px between form fields */
  .form-section-spacing { @apply space-y-6; } /* 24px between form sections */
  .form-group-spacing { @apply space-y-8; } /* 32px between form groups */

  /* Compact page layout utilities */
  .page-container {
    @apply container mx-auto px-4 sm:px-6 lg:px-8;
  }

  .page-content {
    @apply py-4 space-y-4;
  }

  /* Responsive table utilities */
  .table-container {
    @apply w-full overflow-x-auto;
  }

  .table-responsive {
    @apply min-w-full;
  }

  /* Admin layout utilities */
  .admin-content {
    @apply min-w-0 w-full overflow-x-auto;
  }

  .admin-table-wrapper {
    @apply overflow-x-auto -mx-4 sm:-mx-6 lg:-mx-8;
  }

  .admin-table-inner {
    @apply inline-block min-w-full py-2 align-middle px-4 sm:px-6 lg:px-8;
  }

  .page-header-spacing {
    @apply mb-4;
  }

  /* Touch target minimum size */
  .touch-target {
    @apply min-h-11 min-w-11; /* 44px minimum touch target */
  }

  /* Compact button spacing */
  .button-group {
    @apply flex items-center gap-2;
  }

  .button-group-sm {
    @apply flex items-center gap-1;
  }

  /* Compact component spacing */
  .component-spacing {
    @apply space-y-4;
  }

  .component-spacing-sm {
    @apply space-y-3;
  }

  /* Compact grid spacing */
  .grid-spacing {
    @apply gap-4;
  }

  .grid-spacing-sm {
    @apply gap-3;
  }
}

@layer utilities {
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
}

html {
  height: 100%;
  width: 100%;
  overflow-x: hidden;
  overflow-y: auto;
}

/* Animation utilities */
@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}

@keyframes slideInFromBottom {
  from {
    transform: translateY(20px);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInFromTop {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }

  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {

  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

.reduce-motion *,
.reduce-motion *::before,
.reduce-motion *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

/* React Day Picker Calendar Styles */
.rdp {
  margin: 0;
  font-family: inherit;
}

/* Calendar container */
.rdp-months {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  min-width: 280px;
}

.rdp-month {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Calendar header */
.rdp-caption {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  padding: 0.5rem 0;
  margin-bottom: 0.5rem;
}

.rdp-caption_label {
  font-size: 0.875rem;
  font-weight: 500;
  color: hsl(var(--foreground));
}

/* Navigation buttons */
.rdp-nav {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.rdp-nav_button {
  position: absolute;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1.75rem;
  height: 1.75rem;
  padding: 0;
  border: 1px solid hsl(var(--border));
  border-radius: 0.375rem;
  background-color: transparent;
  color: hsl(var(--foreground));
  opacity: 0.5;
  transition: opacity 0.2s;
  cursor: pointer;
}

.rdp-nav_button:hover {
  opacity: 1;
  background-color: hsl(var(--accent));
}

.rdp-nav_button_previous {
  left: 0.25rem;
}

.rdp-nav_button_next {
  right: 0.25rem;
}

/* Calendar table */
.rdp-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

/* Header row with day names */
.rdp-head_row {
  display: flex;
  width: 100%;
  margin-bottom: 0.5rem;
}

.rdp-head_cell {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 2rem;
  font-size: 0.75rem;
  font-weight: 500;
  color: hsl(var(--muted-foreground));
  text-transform: uppercase;
  letter-spacing: 0.025em;
  min-width: 2.25rem;
  padding: 0 0.125rem;
}

/* Calendar rows */
.rdp-row {
  display: flex;
  width: 100%;
  margin-bottom: 0.25rem;
}

/* Calendar cells */
.rdp-cell {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 2.25rem;
  position: relative;
}

/* Day buttons */
.rdp-day {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 0.375rem;
  background-color: transparent;
  color: hsl(var(--foreground));
  font-size: 0.875rem;
  font-weight: normal;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
}

.rdp-day:hover {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

/* Today */
.rdp-day_today {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
  font-weight: 500;
}

/* Selected day */
.rdp-day_selected {
  background-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
  font-weight: 500;
}

.rdp-day_selected:hover {
  background-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
}

/* Disabled days */
.rdp-day_disabled {
  color: hsl(var(--muted-foreground));
  opacity: 0.5;
  cursor: not-allowed;
}

.rdp-day_disabled:hover {
  background-color: transparent;
}

/* Outside month days */
.rdp-day_outside {
  color: hsl(var(--muted-foreground));
  opacity: 0.5;
}

/* Focus styles */
.rdp-day:focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* Range selection */
.rdp-day_range_start,
.rdp-day_range_end {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.rdp-day_range_middle {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

/* Hidden days */
.rdp-day_hidden {
  visibility: hidden;
}

/* Responsive design */
@media (max-width: 640px) {
  .rdp-months {
    min-width: 260px;
  }

  .rdp-cell {
    height: 2rem;
  }

  .rdp-day {
    width: 1.75rem;
    height: 1.75rem;
    font-size: 0.8rem;
  }
}

/* Fix for Select dropdown size issue */
[data-radix-select-trigger] {
  width: 100% !important;
  min-width: 0 !important;
  max-width: 100% !important;
  flex: 1 !important;
}

[data-radix-select-trigger] [data-radix-select-value] {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
  max-width: calc(100% - 2rem) !important;
  flex: 1 !important;
}

/* More specific fix for select triggers */
button[data-slot="select-trigger"] {
  width: 100% !important;
  min-width: 0 !important;
  max-width: 100% !important;
}

button[data-slot="select-trigger"] span[data-slot="select-value"] {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
  max-width: calc(100% - 2rem) !important;
  display: block !important;
  width: 100% !important;
  flex: 1 !important;
}

/* Force all form controls to maintain consistent width */
.grid > div > div > button[data-slot="select-trigger"] {
  width: 100% !important;
  min-width: 0 !important;
  max-width: 100% !important;
}

/* Ensure FormControl containers don't expand */
[data-slot="form-control"] {
  width: 100% !important;
  min-width: 0 !important;
  max-width: 100% !important;
}

/* Specific fix for SPV form dropdowns */
.grid .grid-cols-1.md\\:grid-cols-2 > div button[data-slot="select-trigger"] {
  width: 100% !important;
  min-width: 0 !important;
  max-width: 100% !important;
}

/* Force select value text to truncate */
button[data-slot="select-trigger"] > span {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
  max-width: calc(100% - 2rem) !important;
  display: block !important;
}

/* Ultimate fix for Radix Select width issues */
[data-radix-collection-item] {
  width: auto !important;
}

/* Prevent select trigger from growing beyond container */
button[role="combobox"][data-state] {
  width: 100% !important;
  min-width: 0 !important;
  max-width: 100% !important;
}

button[role="combobox"][data-state] > span[data-radix-select-value] {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
  max-width: calc(100% - 2rem) !important;
  display: block !important;
}