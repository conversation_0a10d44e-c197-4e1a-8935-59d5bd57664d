"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useSPVUser } from "@/components/auth/client-spv-auth-guard";
import {
  FolderOpen,
  Search,
  MapPin,
  Calendar,
  TrendingUp,
  Upload,
  Eye,
  BarChart3,
} from "lucide-react";
import Link from "next/link";

interface ProjectWithStats {
  id: string;
  name: string;
  description?: string;
  type: string;
  status: string;
  location?: string;
  estimatedReductions?: number;
  createdAt: string;
  organization: {
    id: string;
    name: string;
  };
  spv?: {
    id: string;
    name: string;
  };
  assignment: {
    id: string;
    assignedAt: string;
    assignedBy: string;
    isActive: boolean;
  };
  statistics: {
    totalUnitLogs: number;
    draftCount: number;
    submittedCount: number;
    verifiedCount: number;
    approvedCount: number;
    rejectedCount: number;
    userUnitLogs: number;
    completionRate: number;
    rejectionRate: number;
  };
}

export default function SPVProjectsClient() {
  const { spvUser, loading: userLoading, error: userError } = useSPVUser();
  const [projects, setProjects] = useState<ProjectWithStats[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    if (spvUser) {
      fetchProjects();
    }
  }, [spvUser]);

  const fetchProjects = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/spv/projects");
      if (response.ok) {
        const data = await response.json();
        setProjects(data.data.projects);
      } else {
        setError("Failed to fetch projects");
      }
    } catch (err) {
      setError("Error fetching projects");
      console.error("Projects fetch error:", err);
    } finally {
      setLoading(false);
    }
  };

  // Filter projects based on search term
  const filteredProjects = projects.filter(project =>
    project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    project.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (project.location && project.location.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'COMPLETED':
        return 'bg-blue-100 text-blue-800';
      case 'SUSPENDED':
        return 'bg-orange-100 text-orange-800';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (userLoading || loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <Skeleton className="h-8 w-48 mb-2" />
            <Skeleton className="h-4 w-96" />
          </div>
        </div>
        
        <div className="flex gap-4">
          <Skeleton className="h-10 w-80" />
        </div>
        
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {[...Array(6)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-4 w-24" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-3/4 mb-4" />
                <div className="flex gap-2">
                  <Skeleton className="h-8 w-20" />
                  <Skeleton className="h-8 w-20" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (userError || error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>
          {userError || error}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Assigned Projects</h1>
          <p className="text-muted-foreground">
            Projects you have access to for data entry and verification • {filteredProjects.length} project{filteredProjects.length !== 1 ? 's' : ''}
          </p>
        </div>
        <div className="flex space-x-3">
          <Button asChild>
            <Link href="/spv/data-entry">
              <Upload className="h-4 w-4 mr-2" />
              Enter Data
            </Link>
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex gap-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search projects..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
            autoComplete="off"
          />
        </div>
      </div>

      {/* Projects Grid */}
      {filteredProjects.length > 0 ? (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredProjects.map((project) => (
            <Card key={project.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg">{project.name}</CardTitle>
                    <CardDescription className="mt-1">
                      {project.type} • {project.organization.name}
                    </CardDescription>
                  </div>
                  <Badge className={getStatusColor(project.status)}>
                    {project.status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Project Details */}
                  <div className="space-y-2">
                    {project.location && (
                      <div className="flex items-center text-sm text-muted-foreground">
                        <MapPin className="h-4 w-4 mr-2" />
                        {project.location}
                      </div>
                    )}
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Calendar className="h-4 w-4 mr-2" />
                      Assigned {new Date(project.assignment.assignedAt).toLocaleDateString()}
                    </div>
                    {project.estimatedReductions && (
                      <div className="flex items-center text-sm text-muted-foreground">
                        <TrendingUp className="h-4 w-4 mr-2" />
                        {project.estimatedReductions.toLocaleString()} tCO2e estimated
                      </div>
                    )}
                  </div>

                  {/* Statistics */}
                  <div className="grid grid-cols-2 gap-3 text-sm">
                    <div className="bg-gray-50 p-2 rounded">
                      <div className="font-medium">{project.statistics.totalUnitLogs}</div>
                      <div className="text-muted-foreground">Total Entries</div>
                    </div>
                    <div className="bg-blue-50 p-2 rounded">
                      <div className="font-medium">{project.statistics.userUnitLogs}</div>
                      <div className="text-muted-foreground">Your Entries</div>
                    </div>
                    <div className="bg-green-50 p-2 rounded">
                      <div className="font-medium">{project.statistics.completionRate}%</div>
                      <div className="text-muted-foreground">Completion</div>
                    </div>
                    <div className="bg-yellow-50 p-2 rounded">
                      <div className="font-medium">{project.statistics.draftCount}</div>
                      <div className="text-muted-foreground">Drafts</div>
                    </div>
                  </div>

                  {/* Progress Bar */}
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Verification Progress</span>
                      <span>{project.statistics.completionRate}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-green-600 h-2 rounded-full transition-all"
                        style={{ width: `${project.statistics.completionRate}%` }}
                      />
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2 pt-2">
                    <Button size="sm" className="flex-1" asChild>
                      <Link href={`/spv/projects/${project.id}`}>
                        <Eye className="h-4 w-4 mr-2" />
                        View Details
                      </Link>
                    </Button>
                    <Button size="sm" variant="outline" asChild>
                      <Link href={`/spv/data-entry?project=${project.id}`}>
                        <Upload className="h-4 w-4 mr-2" />
                        Add Data
                      </Link>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <FolderOpen className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">
            {searchTerm ? "No projects found" : "No Projects Assigned"}
          </h3>
          <p className="text-muted-foreground mb-4">
            {searchTerm
              ? "Try adjusting your search criteria"
              : "You don't have any projects assigned yet. Contact your SPV administrator to get project assignments."
            }
          </p>
          {searchTerm && (
            <Button variant="outline" onClick={() => setSearchTerm("")}>
              Clear Search
            </Button>
          )}
        </div>
      )}

      {/* Summary Stats */}
      {filteredProjects.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Project Summary</CardTitle>
            <CardDescription>
              Overview of your assigned projects
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {filteredProjects.length}
                </div>
                <div className="text-sm text-muted-foreground">Total Projects</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {filteredProjects.filter(p => p.status === 'ACTIVE').length}
                </div>
                <div className="text-sm text-muted-foreground">Active Projects</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">
                  {filteredProjects.reduce((sum, p) => sum + p.statistics.userUnitLogs, 0)}
                </div>
                <div className="text-sm text-muted-foreground">Your Entries</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {filteredProjects.reduce((sum, p) => sum + p.statistics.draftCount, 0)}
                </div>
                <div className="text-sm text-muted-foreground">Draft Entries</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
