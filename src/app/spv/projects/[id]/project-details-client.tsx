"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Ta<PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useSPVUser, useSPVPermissions } from "@/components/auth/client-spv-auth-guard";
import {
  ArrowLeft,
  MapPin,
  Calendar,
  TrendingUp,
  Building,
  FileText,
  Upload,
  Eye,
  Edit,
  Trash2,
  CheckCircle,
  Clock,
  AlertCircle,
  BarChart3,
  Users,
} from "lucide-react";
import Link from "next/link";
import { useR<PERSON><PERSON> } from "next/navigation";

interface ProjectDetails {
  id: string;
  name: string;
  description?: string;
  type: string;
  status: string;
  location?: string;
  estimatedReductions?: number;
  createdAt: string;
  organization: {
    id: string;
    name: string;
    legalName: string;
  };
  spv?: {
    id: string;
    name: string;
    purpose?: string;
    jurisdiction?: string;
  };
  documents: Array<{
    id: string;
    name: string;
    type: string;
    url: string;
    status: string;
    createdAt: string;
  }>;
  assignment: {
    id: string;
    assignedAt: string;
    assignedBy: string;
    isActive: boolean;
  };
  statistics: {
    totalUnitLogs: number;
    draftCount: number;
    submittedCount: number;
    verifiedCount: number;
    approvedCount: number;
    rejectedCount: number;
    userUnitLogs: number;
    completionRate: number;
    rejectionRate: number;
  };
  recentUnitLogs: Array<{
    id: string;
    logDate: string;
    unitType: string;
    quantity: number;
    verificationStatus: string;
    createdAt: string;
    logger: {
      id: string;
      name: string;
      email: string;
    };
  }>;
}

interface ProjectDetailsClientProps {
  projectId: string;
}

export default function ProjectDetailsClient({ projectId }: ProjectDetailsClientProps) {
  const router = useRouter();
  const { spvUser, loading: userLoading, error: userError } = useSPVUser();
  const { hasPermission } = useSPVPermissions();
  const [project, setProject] = useState<ProjectDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (spvUser) {
      fetchProjectDetails();
    }
  }, [spvUser, projectId]);

  const fetchProjectDetails = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/spv/projects/${projectId}`);
      if (response.ok) {
        const data = await response.json();
        setProject(data.data.project);
      } else if (response.status === 403) {
        setError("Access denied. This project is not assigned to you.");
      } else if (response.status === 404) {
        setError("Project not found.");
      } else {
        setError("Failed to fetch project details");
      }
    } catch (err) {
      setError("Error fetching project details");
      console.error("Project details fetch error:", err);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'COMPLETED':
        return 'bg-blue-100 text-blue-800';
      case 'SUSPENDED':
        return 'bg-orange-100 text-orange-800';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getVerificationStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return 'bg-gray-100 text-gray-800';
      case 'SUBMITTED_FOR_VERIFICATION':
        return 'bg-yellow-100 text-yellow-800';
      case 'VERIFIED':
        return 'bg-green-100 text-green-800';
      case 'REJECTED':
        return 'bg-red-100 text-red-800';
      case 'SPV_APPROVED':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'VERIFIED':
      case 'SPV_APPROVED':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'REJECTED':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'SUBMITTED_FOR_VERIFICATION':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  if (userLoading || loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Skeleton className="h-10 w-10" />
          <div>
            <Skeleton className="h-8 w-64 mb-2" />
            <Skeleton className="h-4 w-96" />
          </div>
        </div>
        
        <div className="grid gap-6 md:grid-cols-3">
          <div className="md:col-span-2">
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[...Array(4)].map((_, i) => (
                    <Skeleton key={i} className="h-4 w-full" />
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
          <div>
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-24" />
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[...Array(3)].map((_, i) => (
                    <Skeleton key={i} className="h-12 w-full" />
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  if (userError || error) {
    return (
      <div className="space-y-4">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
        </div>
        <Alert variant="destructive">
          <AlertDescription>
            {userError || error}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="space-y-4">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
        </div>
        <Alert>
          <AlertDescription>
            Project not found or you don't have access to it.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const canCreateUnitLogs = hasPermission("create:unit_log");

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-start justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/spv/projects">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Projects
            </Link>
          </Button>
          <div>
            <div className="flex items-center gap-3 mb-2">
              <h1 className="text-3xl font-bold tracking-tight">{project.name}</h1>
              <Badge className={getStatusColor(project.status)}>
                {project.status}
              </Badge>
            </div>
            <p className="text-muted-foreground">
              {project.type} • {project.organization.name}
            </p>
          </div>
        </div>
        <div className="flex gap-3">
          {canCreateUnitLogs && (
            <Button asChild>
              <Link href={`/spv/data-entry?project=${project.id}`}>
                <Upload className="h-4 w-4 mr-2" />
                Add Data
              </Link>
            </Button>
          )}
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        {/* Main Content */}
        <div className="md:col-span-2">
          <Tabs defaultValue="overview" className="space-y-6">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="unit-logs">Unit Logs</TabsTrigger>
              <TabsTrigger value="documents">Documents</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              {/* Project Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Project Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {project.description && (
                    <div>
                      <h4 className="font-medium mb-2">Description</h4>
                      <p className="text-muted-foreground">{project.description}</p>
                    </div>
                  )}
                  
                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <h4 className="font-medium mb-2">Project Type</h4>
                      <p className="text-muted-foreground">{project.type}</p>
                    </div>
                    
                    {project.location && (
                      <div>
                        <h4 className="font-medium mb-2">Location</h4>
                        <div className="flex items-center text-muted-foreground">
                          <MapPin className="h-4 w-4 mr-2" />
                          {project.location}
                        </div>
                      </div>
                    )}
                    
                    <div>
                      <h4 className="font-medium mb-2">Created</h4>
                      <div className="flex items-center text-muted-foreground">
                        <Calendar className="h-4 w-4 mr-2" />
                        {new Date(project.createdAt).toLocaleDateString()}
                      </div>
                    </div>
                    
                    {project.estimatedReductions && (
                      <div>
                        <h4 className="font-medium mb-2">Estimated Reductions</h4>
                        <div className="flex items-center text-muted-foreground">
                          <TrendingUp className="h-4 w-4 mr-2" />
                          {project.estimatedReductions.toLocaleString()} tCO2e
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Organization & SPV Info */}
                  <div className="grid gap-4 md:grid-cols-2 pt-4 border-t">
                    <div>
                      <h4 className="font-medium mb-2">Organization</h4>
                      <div className="flex items-center text-muted-foreground">
                        <Building className="h-4 w-4 mr-2" />
                        <div>
                          <div>{project.organization.name}</div>
                          {project.organization.legalName !== project.organization.name && (
                            <div className="text-xs">{project.organization.legalName}</div>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    {project.spv && (
                      <div>
                        <h4 className="font-medium mb-2">SPV</h4>
                        <div className="flex items-center text-muted-foreground">
                          <Users className="h-4 w-4 mr-2" />
                          <div>
                            <div>{project.spv.name}</div>
                            {project.spv.jurisdiction && (
                              <div className="text-xs">{project.spv.jurisdiction}</div>
                            )}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Assignment Info */}
                  <div className="pt-4 border-t">
                    <h4 className="font-medium mb-2">Assignment Details</h4>
                    <div className="text-sm text-muted-foreground">
                      Assigned on {new Date(project.assignment.assignedAt).toLocaleDateString()}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="unit-logs" className="space-y-6">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>Recent Unit Logs</CardTitle>
                      <CardDescription>
                        Latest data entries for this project
                      </CardDescription>
                    </div>
                    {canCreateUnitLogs && (
                      <Button size="sm" asChild>
                        <Link href={`/spv/data-entry?project=${project.id}`}>
                          <Upload className="h-4 w-4 mr-2" />
                          Add Entry
                        </Link>
                      </Button>
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  {project.recentUnitLogs.length > 0 ? (
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Date</TableHead>
                            <TableHead>Type</TableHead>
                            <TableHead>Quantity</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Logger</TableHead>
                            <TableHead>Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {project.recentUnitLogs.map((unitLog) => (
                            <TableRow key={unitLog.id}>
                              <TableCell>
                                {new Date(unitLog.logDate).toLocaleDateString()}
                              </TableCell>
                              <TableCell>{unitLog.unitType}</TableCell>
                              <TableCell>{unitLog.quantity}</TableCell>
                              <TableCell>
                                <div className="flex items-center gap-2">
                                  {getStatusIcon(unitLog.verificationStatus)}
                                  <Badge className={getVerificationStatusColor(unitLog.verificationStatus)}>
                                    {unitLog.verificationStatus.replace('_', ' ')}
                                  </Badge>
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="text-sm">
                                  <div>{unitLog.logger.name}</div>
                                  <div className="text-muted-foreground text-xs">
                                    {unitLog.logger.email}
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="flex gap-2">
                                  <Button size="sm" variant="outline">
                                    <Eye className="h-4 w-4" />
                                  </Button>
                                  {unitLog.verificationStatus === 'DRAFT' && unitLog.logger.id === spvUser?.userId && (
                                    <>
                                      <Button size="sm" variant="outline">
                                        <Edit className="h-4 w-4" />
                                      </Button>
                                      <Button size="sm" variant="outline">
                                        <Trash2 className="h-4 w-4" />
                                      </Button>
                                    </>
                                  )}
                                </div>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">No unit logs yet</h3>
                      <p className="text-muted-foreground mb-4">
                        No data entries have been created for this project yet.
                      </p>
                      {canCreateUnitLogs && (
                        <Button asChild>
                          <Link href={`/spv/data-entry?project=${project.id}`}>
                            <Upload className="h-4 w-4 mr-2" />
                            Create First Entry
                          </Link>
                        </Button>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="documents" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Project Documents</CardTitle>
                  <CardDescription>
                    Supporting documents and files for this project
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {project.documents.length > 0 ? (
                    <div className="space-y-3">
                      {project.documents.map((document) => (
                        <div key={document.id} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center gap-3">
                            <FileText className="h-5 w-5 text-muted-foreground" />
                            <div>
                              <div className="font-medium">{document.name}</div>
                              <div className="text-sm text-muted-foreground">
                                {document.type} • {new Date(document.createdAt).toLocaleDateString()}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">{document.status}</Badge>
                            <Button size="sm" variant="outline" asChild>
                              <a href={document.url} target="_blank" rel="noopener noreferrer">
                                <Eye className="h-4 w-4" />
                              </a>
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">No documents</h3>
                      <p className="text-muted-foreground">
                        No documents have been uploaded for this project yet.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Statistics */}
          <Card>
            <CardHeader>
              <CardTitle>Statistics</CardTitle>
              <CardDescription>
                Data entry and verification progress
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div className="bg-gray-50 p-3 rounded text-center">
                    <div className="text-2xl font-bold">{project.statistics.totalUnitLogs}</div>
                    <div className="text-muted-foreground">Total Entries</div>
                  </div>
                  <div className="bg-blue-50 p-3 rounded text-center">
                    <div className="text-2xl font-bold">{project.statistics.userUnitLogs}</div>
                    <div className="text-muted-foreground">Your Entries</div>
                  </div>
                  <div className="bg-green-50 p-3 rounded text-center">
                    <div className="text-2xl font-bold">{project.statistics.completionRate}%</div>
                    <div className="text-muted-foreground">Completion</div>
                  </div>
                  <div className="bg-yellow-50 p-3 rounded text-center">
                    <div className="text-2xl font-bold">{project.statistics.draftCount}</div>
                    <div className="text-muted-foreground">Drafts</div>
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Verification Progress</span>
                    <span>{project.statistics.completionRate}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-green-600 h-2 rounded-full transition-all"
                      style={{ width: `${project.statistics.completionRate}%` }}
                    />
                  </div>
                </div>

                {/* Status Breakdown */}
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Submitted:</span>
                    <span>{project.statistics.submittedCount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Verified:</span>
                    <span>{project.statistics.verifiedCount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Approved:</span>
                    <span>{project.statistics.approvedCount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Rejected:</span>
                    <span>{project.statistics.rejectedCount}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {canCreateUnitLogs && (
                <Button className="w-full" asChild>
                  <Link href={`/spv/data-entry?project=${project.id}`}>
                    <Upload className="h-4 w-4 mr-2" />
                    Add Data Entry
                  </Link>
                </Button>
              )}
              <Button variant="outline" className="w-full" asChild>
                <Link href={`/spv/verification?project=${project.id}`}>
                  <Eye className="h-4 w-4 mr-2" />
                  View All Entries
                </Link>
              </Button>
              <Button variant="outline" className="w-full" asChild>
                <Link href="/spv/analytics">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  View Analytics
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
