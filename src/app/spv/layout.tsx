"use client";

import { ReactNode, useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Navbar } from "@/components/layout/navbar";
import { ClientSPVAuthGuard } from "@/components/auth/client-spv-auth-guard";
import { useSPVUser } from "@/components/auth/client-spv-auth-guard";
import { TooltipProvider, Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { PageTransition } from "@/components/ui/animated";
import {
  ChevronLeft,
  ChevronRight,
  Menu,
  LayoutDashboard,
  FolderOpen,
  Upload,
  ClipboardCheck,
  BarChart3,
  FileText,
  Users,
  Settings,
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

interface SPVLayoutProps {
  children: ReactNode;
}

export default function SPVLayout({ children }: SPVLayoutProps) {
  const pathname = usePathname();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { spvUser } = useSPVUser();

  // Close mobile menu when pathname changes
  useEffect(() => {
    setMobileMenuOpen(false);
  }, [pathname]);

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  // Navigation structure based on user role
  const navigation = [
    {
      title: "Overview",
      items: [
        { title: "Dashboard", href: "/spv/dashboard", icon: LayoutDashboard },
        { title: "Projects", href: "/spv/projects", icon: FolderOpen },
      ]
    },
    {
      title: "Data Management",
      items: [
        { title: "Data Entry", href: "/spv/data-entry", icon: Upload, roles: ["SITE_WORKER", "PROJECT_MANAGER", "SPV_ADMIN"] },
        { title: "Verification Queue", href: "/spv/verification", icon: ClipboardCheck, roles: ["PROJECT_MANAGER", "SPV_ADMIN"] },
      ]
    },
    {
      title: "Analytics & Reports",
      items: [
        { title: "Analytics", href: "/spv/analytics", icon: BarChart3, roles: ["PROJECT_MANAGER", "SPV_ADMIN"] },
        { title: "Reports", href: "/spv/reports", icon: FileText, roles: ["PROJECT_MANAGER", "SPV_ADMIN"] },
      ]
    },
    {
      title: "Administration",
      items: [
        { title: "User Management", href: "/spv/users", icon: Users, roles: ["SPV_ADMIN"] },
        { title: "Settings", href: "/spv/settings", icon: Settings, roles: ["SPV_ADMIN"] },
      ]
    }
  ];

  // Filter navigation items based on user role
  const filteredNavigation = navigation.map(section => ({
    ...section,
    items: section.items.filter(item =>
      !item.roles || !spvUser?.role || item.roles.includes(spvUser.role)
    )
  })).filter(section => section.items.length > 0);

  // Get page title based on pathname
  const getPageTitle = (pathname: string) => {
    if (pathname === "/spv" || pathname === "/spv/dashboard") return "SPV Dashboard";
    if (pathname === "/spv/projects") return "Assigned Projects";
    if (pathname.startsWith("/spv/projects/")) return "Project Details";
    if (pathname === "/spv/data-entry") return "Data Entry";
    if (pathname === "/spv/verification") return "Verification Queue";
    if (pathname === "/spv/analytics") return "Analytics";
    if (pathname === "/spv/reports") return "Reports";
    if (pathname === "/spv/users") return "User Management";
    if (pathname === "/spv/settings") return "Settings";
    return "SPV Portal";
  };

  return (
    <ClientSPVAuthGuard>
      <TooltipProvider>
        <div className="flex h-screen">
          {/* SPV Sidebar */}
          <motion.aside
            className="fixed left-0 top-0 z-40 h-full bg-white border-r border-gray-200 shadow-sm hidden md:flex flex-col"
            animate={{
              width: isCollapsed ? '4rem' : '15rem'
            }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
          >
            {/* SPV Sidebar Header */}
            <div className="flex items-center justify-between p-3 border-b border-gray-200">
              <motion.div
                className="flex items-center gap-2"
                animate={{
                  opacity: isCollapsed ? 0 : 1,
                }}
                transition={{ duration: 0.2 }}
              >
                <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">SPV</span>
                </div>
                {!isCollapsed && (
                  <span className="font-semibold text-gray-900">Portal</span>
                )}
              </motion.div>

              {/* Collapse Toggle */}
              <motion.button
                onClick={toggleCollapse}
                className="p-1 rounded hover:bg-gray-100 transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {isCollapsed ? (
                  <ChevronRight className="h-4 w-4 text-gray-600" />
                ) : (
                  <ChevronLeft className="h-4 w-4 text-gray-600" />
                )}
              </motion.button>
            </div>

            {/* SPV Navigation Menu */}
            <nav className="flex-1 px-2 py-2 space-y-0.5 overflow-y-auto">
              {filteredNavigation.map((section, sectionIndex) => (
                <div key={section.title} className="py-1">
                  {/* Section Title with smooth animation */}
                  <motion.h4
                    className="mb-0.5 rounded px-1.5 py-0.5 text-xs font-semibold text-gray-600"
                    animate={{
                      opacity: isCollapsed ? 0 : 1,
                      height: isCollapsed ? 0 : 'auto',
                      marginBottom: isCollapsed ? 0 : '0.25rem',
                    }}
                    transition={{ duration: 0.3 }}
                    style={{
                      overflow: 'hidden',
                      whiteSpace: 'nowrap'
                    }}
                  >
                    {section.title}
                  </motion.h4>

                  {section.items?.map((item, itemIndex) => {
                    // Check if current path matches or starts with the navigation item's path
                    const isActive = pathname === item.href ||
                      (item.href !== '/spv/dashboard' && pathname.startsWith(item.href + '/'));

                    // Create the navigation item with motion
                    const NavItem = (
                      <motion.div
                        key={item.href}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.1 * (sectionIndex * 3 + itemIndex), duration: 0.3 }}
                      >
                        <Link
                          href={item.href}
                          className={cn(
                            "group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-all duration-200",
                            isActive
                              ? "bg-green-50 text-green-700 border-l-3 border-green-700"
                              : "text-gray-600 hover:bg-gray-50 hover:text-gray-900",
                            isCollapsed && "justify-center px-2"
                          )}
                        >
                          <item.icon
                            className={cn(
                              "flex-shrink-0 transition-colors duration-200",
                              isActive ? "text-green-700" : "text-gray-400 group-hover:text-gray-500",
                              isCollapsed ? "h-5 w-5" : "h-4 w-4 mr-3"
                            )}
                          />
                          <motion.span
                            className="truncate"
                            animate={{
                              opacity: isCollapsed ? 0 : 1,
                              width: isCollapsed ? 0 : 'auto',
                            }}
                            transition={{ duration: 0.2 }}
                            style={{
                              overflow: 'hidden',
                              whiteSpace: 'nowrap'
                            }}
                          >
                            {item.title}
                          </motion.span>
                        </Link>
                      </motion.div>
                    );

                    // Wrap with tooltip if collapsed
                    if (isCollapsed) {
                      return (
                        <Tooltip key={item.href}>
                          <TooltipTrigger asChild>
                            {NavItem}
                          </TooltipTrigger>
                          <TooltipContent side="right" className="ml-2">
                            {item.title}
                          </TooltipContent>
                        </Tooltip>
                      );
                    }

                    return NavItem;
                  })}
                </div>
              ))}
            </nav>
          </motion.aside>

          {/* Mobile Sidebar Overlay */}
          <AnimatePresence>
            {mobileMenuOpen && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 z-50 md:hidden"
              >
                <div className="absolute inset-0 bg-black bg-opacity-50" onClick={toggleMobileMenu} />
                <motion.aside
                  initial={{ x: -300 }}
                  animate={{ x: 0 }}
                  exit={{ x: -300 }}
                  transition={{ type: "spring", damping: 25, stiffness: 200 }}
                  className="absolute left-0 top-0 h-full w-64 bg-white shadow-xl"
                >
                  {/* Mobile navigation content - similar to desktop but always expanded */}
                  <div className="flex items-center justify-between p-4 border-b border-gray-200">
                    <div className="flex items-center space-x-2">
                      <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
                        <span className="text-white font-bold text-sm">SPV</span>
                      </div>
                      <span className="font-semibold text-gray-900">Portal</span>
                    </div>
                    <Button variant="ghost" size="sm" onClick={toggleMobileMenu}>
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                  </div>

                  {/* Mobile Navigation Menu */}
                  <nav className="flex-1 px-4 py-4 space-y-2 overflow-y-auto">
                    {filteredNavigation.map((section) => (
                      <div key={section.title} className="py-2">
                        <h4 className="mb-2 px-2 text-xs font-semibold text-gray-600 uppercase tracking-wider">
                          {section.title}
                        </h4>
                        {section.items?.map((item) => {
                          const isActive = pathname === item.href ||
                            (item.href !== '/spv/dashboard' && pathname.startsWith(item.href + '/'));

                          return (
                            <Link
                              key={item.href}
                              href={item.href}
                              className={cn(
                                "flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",
                                isActive
                                  ? "bg-green-50 text-green-700"
                                  : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                              )}
                              onClick={toggleMobileMenu}
                            >
                              <item.icon className="h-4 w-4 mr-3 flex-shrink-0" />
                              {item.title}
                            </Link>
                          );
                        })}
                      </div>
                    ))}
                  </nav>
                </motion.aside>
              </motion.div>
            )}
          </AnimatePresence>

          {/* SPV Main content area */}
          <div
            className="flex flex-col flex-1 transition-all duration-300 ml-0 md:ml-[var(--sidebar-width)] min-w-0"
            style={{
              '--sidebar-width': isCollapsed ? '4rem' : '15rem'
            } as React.CSSProperties}
          >
            {/* SPV Navbar */}
            <div className="relative">
              <Navbar
                variant="spv"
                title={getPageTitle(pathname)}
                className="relative"
                onMobileMenuClick={toggleMobileMenu}
              />
            </div>

            {/* SPV Main content */}
            <main className="flex-1 overflow-y-auto overflow-x-auto bg-green-50 min-w-0">
              <div className="page-container page-content p-6 min-w-0">
                <div className="min-w-0 w-full">
                  <PageTransition>
                    {children}
                  </PageTransition>
                </div>
              </div>
            </main>
          </div>
        </div>
      </TooltipProvider>
    </ClientSPVAuthGuard>
  );
}
