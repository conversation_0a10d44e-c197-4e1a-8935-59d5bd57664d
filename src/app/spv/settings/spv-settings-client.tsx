"use client";

import { useState, useEffect } from "react";
import { 
  <PERSON>, 
  CardContent, 
  CardDescription, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Settings, 
  User, 
  Bell, 
  Shield, 
  Save,
  RefreshCw,
  Mail,
  Phone,
  MapPin,
  Building
} from "lucide-react";
import { toast } from "sonner";
import { useSPVUser } from "@/components/auth/client-spv-auth-guard";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";

interface SPVSettings {
  notifications: {
    emailNotifications: boolean;
    dataEntryAlerts: boolean;
    verificationUpdates: boolean;
    weeklyReports: boolean;
  };
  preferences: {
    timezone: string;
    dateFormat: string;
    language: string;
  };
  profile: {
    name: string;
    email: string;
    phone?: string;
    jobTitle?: string;
    department?: string;
  };
}

export function SPVSettingsClient() {
  const { spvUser, loading: userLoading } = useSPVUser();
  const [settings, setSettings] = useState<SPVSettings>({
    notifications: {
      emailNotifications: true,
      dataEntryAlerts: true,
      verificationUpdates: true,
      weeklyReports: false,
    },
    preferences: {
      timezone: "UTC",
      dateFormat: "MM/DD/YYYY",
      language: "en",
    },
    profile: {
      name: "",
      email: "",
      phone: "",
      jobTitle: "",
      department: "",
    },
  });
  const [loading, setLoading] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    if (spvUser?.user) {
      setSettings(prev => ({
        ...prev,
        profile: {
          name: spvUser.user.name || "",
          email: spvUser.user.email || "",
          phone: spvUser.user.phone || "",
          jobTitle: spvUser.user.jobTitle || "",
          department: spvUser.user.department || "",
        },
      }));
    }
  }, [spvUser]);

  const handleSaveSettings = async () => {
    try {
      setLoading(true);
      
      const response = await fetch("/api/spv/settings", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(settings),
      });

      if (!response.ok) {
        throw new Error("Failed to save settings");
      }

      toast.success("Settings saved successfully");
      setHasChanges(false);
    } catch (error) {
      console.error("Error saving settings:", error);
      toast.error("Failed to save settings");
    } finally {
      setLoading(false);
    }
  };

  const updateSettings = (section: keyof SPVSettings, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value,
      },
    }));
    setHasChanges(true);
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case "SITE_WORKER":
        return "bg-blue-100 text-blue-800";
      case "PROJECT_MANAGER":
        return "bg-green-100 text-green-800";
      case "SPV_ADMIN":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (userLoading) {
    return (
      <div className="p-6">
        <div className="space-y-6">
          <div className="h-8 w-48 bg-gray-200 rounded animate-pulse" />
          <div className="grid gap-6">
            {[...Array(3)].map((_, i) => (
              <Card key={i}>
                <CardHeader>
                  <div className="h-6 w-32 bg-gray-200 rounded animate-pulse" />
                  <div className="h-4 w-64 bg-gray-200 rounded animate-pulse" />
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[...Array(3)].map((_, j) => (
                      <div key={j} className="h-10 bg-gray-200 rounded animate-pulse" />
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
            <p className="text-gray-600">Manage your SPV portal preferences and profile</p>
          </div>
          {hasChanges && (
            <Button onClick={handleSaveSettings} disabled={loading}>
              {loading ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              Save Changes
            </Button>
          )}
        </div>

        <Tabs defaultValue="profile" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="profile">Profile</TabsTrigger>
            <TabsTrigger value="notifications">Notifications</TabsTrigger>
            <TabsTrigger value="preferences">Preferences</TabsTrigger>
          </TabsList>

          {/* Profile Tab */}
          <TabsContent value="profile" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Profile Information
                </CardTitle>
                <CardDescription>
                  Update your personal information and contact details
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* SPV Info */}
                <div className="p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-medium">SPV Information</h3>
                    <Badge className={getRoleBadgeColor(spvUser?.role || "")}>
                      {spvUser?.role?.replace('_', ' ')}
                    </Badge>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">SPV Name:</span>
                      <p className="font-medium">{spvUser?.spv?.name}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">Organization:</span>
                      <p className="font-medium">{spvUser?.spv?.organization?.name}</p>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="name">Full Name</Label>
                    <Input
                      id="name"
                      value={settings.profile.name}
                      onChange={(e) => updateSettings("profile", "name", e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address</Label>
                    <Input
                      id="email"
                      type="email"
                      value={settings.profile.email}
                      onChange={(e) => updateSettings("profile", "email", e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input
                      id="phone"
                      value={settings.profile.phone}
                      onChange={(e) => updateSettings("profile", "phone", e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="jobTitle">Job Title</Label>
                    <Input
                      id="jobTitle"
                      value={settings.profile.jobTitle}
                      onChange={(e) => updateSettings("profile", "jobTitle", e.target.value)}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="department">Department</Label>
                  <Input
                    id="department"
                    value={settings.profile.department}
                    onChange={(e) => updateSettings("profile", "department", e.target.value)}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Notifications Tab */}
          <TabsContent value="notifications" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bell className="h-5 w-5" />
                  Notification Preferences
                </CardTitle>
                <CardDescription>
                  Choose what notifications you want to receive
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="emailNotifications">Email Notifications</Label>
                      <p className="text-sm text-gray-500">Receive general email notifications</p>
                    </div>
                    <Switch
                      id="emailNotifications"
                      checked={settings.notifications.emailNotifications}
                      onCheckedChange={(checked) => updateSettings("notifications", "emailNotifications", checked)}
                    />
                  </div>
                  <Separator />
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="dataEntryAlerts">Data Entry Alerts</Label>
                      <p className="text-sm text-gray-500">Get notified when data entry is required</p>
                    </div>
                    <Switch
                      id="dataEntryAlerts"
                      checked={settings.notifications.dataEntryAlerts}
                      onCheckedChange={(checked) => updateSettings("notifications", "dataEntryAlerts", checked)}
                    />
                  </div>
                  <Separator />
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="verificationUpdates">Verification Updates</Label>
                      <p className="text-sm text-gray-500">Receive updates on verification status</p>
                    </div>
                    <Switch
                      id="verificationUpdates"
                      checked={settings.notifications.verificationUpdates}
                      onCheckedChange={(checked) => updateSettings("notifications", "verificationUpdates", checked)}
                    />
                  </div>
                  <Separator />
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="weeklyReports">Weekly Reports</Label>
                      <p className="text-sm text-gray-500">Receive weekly summary reports</p>
                    </div>
                    <Switch
                      id="weeklyReports"
                      checked={settings.notifications.weeklyReports}
                      onCheckedChange={(checked) => updateSettings("notifications", "weeklyReports", checked)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Preferences Tab */}
          <TabsContent value="preferences" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  System Preferences
                </CardTitle>
                <CardDescription>
                  Customize your portal experience
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="timezone">Timezone</Label>
                    <Input
                      id="timezone"
                      value={settings.preferences.timezone}
                      onChange={(e) => updateSettings("preferences", "timezone", e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="dateFormat">Date Format</Label>
                    <Input
                      id="dateFormat"
                      value={settings.preferences.dateFormat}
                      onChange={(e) => updateSettings("preferences", "dateFormat", e.target.value)}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="language">Language</Label>
                  <Input
                    id="language"
                    value={settings.preferences.language}
                    onChange={(e) => updateSettings("preferences", "language", e.target.value)}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
