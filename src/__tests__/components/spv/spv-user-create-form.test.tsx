import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { SPVUserCreateForm } from '@/components/spv/spv-user-create-form';

// Mock fetch
global.fetch = vi.fn();

// Mock toast
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

const mockFetch = vi.mocked(fetch);

describe('SPVUserCreateForm', () => {
  const defaultProps = {
    isOpen: true,
    onClose: vi.fn(),
    onSuccess: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock successful projects fetch
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => ({
        data: {
          projects: [
            {
              id: 'project1',
              name: 'Test Project 1',
              type: 'SOLAR',
              status: 'ACTIVE',
              location: 'Test Location',
              assignedUsers: [],
            },
            {
              id: 'project2',
              name: 'Test Project 2',
              type: 'WIND',
              status: 'ACTIVE',
              assignedUsers: [],
            },
          ],
        },
      }),
    } as Response);
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should render form fields correctly', async () => {
    render(<SPVUserCreateForm {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByLabelText(/full name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/role/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/job title/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/phone number/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/confirm password/i)).toBeInTheDocument();
    });
  });

  it('should fetch projects when form opens', async () => {
    render(<SPVUserCreateForm {...defaultProps} />);

    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledWith('/api/spv/projects?limit=100');
    });
  });

  it('should show project assignment section when checkbox is checked', async () => {
    const user = userEvent.setup();
    render(<SPVUserCreateForm {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByText(/assign to projects now/i)).toBeInTheDocument();
    });

    const checkbox = screen.getByRole('checkbox', { name: /assign to projects now/i });
    await user.click(checkbox);

    await waitFor(() => {
      expect(screen.getByText(/select projects/i)).toBeInTheDocument();
      expect(screen.getByText('Test Project 1')).toBeInTheDocument();
      expect(screen.getByText('Test Project 2')).toBeInTheDocument();
    });
  });

  it('should validate required fields', async () => {
    const user = userEvent.setup();
    render(<SPVUserCreateForm {...defaultProps} />);

    const submitButton = screen.getByRole('button', { name: /create user/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/name is required/i)).toBeInTheDocument();
      expect(screen.getByText(/invalid email address/i)).toBeInTheDocument();
      expect(screen.getByText(/role is required/i)).toBeInTheDocument();
    });
  });

  it('should validate password confirmation', async () => {
    const user = userEvent.setup();
    render(<SPVUserCreateForm {...defaultProps} />);

    const passwordInput = screen.getByLabelText(/^password/i);
    const confirmPasswordInput = screen.getByLabelText(/confirm password/i);

    await user.type(passwordInput, 'password123');
    await user.type(confirmPasswordInput, 'differentpassword');

    const submitButton = screen.getByRole('button', { name: /create user/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/passwords don't match/i)).toBeInTheDocument();
    });
  });

  it('should submit form with valid data', async () => {
    const user = userEvent.setup();
    
    // Mock successful user creation
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: { id: 'newuser1' },
        message: 'SPV user created successfully',
      }),
    } as Response);

    render(<SPVUserCreateForm {...defaultProps} />);

    // Fill in required fields
    await user.type(screen.getByLabelText(/full name/i), 'John Doe');
    await user.type(screen.getByLabelText(/email address/i), '<EMAIL>');
    
    // Select role
    const roleSelect = screen.getByRole('combobox', { name: /role/i });
    await user.click(roleSelect);
    await user.click(screen.getByRole('option', { name: /project manager/i }));

    await user.type(screen.getByLabelText(/job title/i), 'Project Manager');
    await user.type(screen.getByLabelText(/phone number/i), '+1234567890');
    await user.type(screen.getByLabelText(/^password/i), 'password123');
    await user.type(screen.getByLabelText(/confirm password/i), 'password123');

    const submitButton = screen.getByRole('button', { name: /create user/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledWith('/api/spv/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          name: 'John Doe',
          role: 'PROJECT_MANAGER',
          jobTitle: 'Project Manager',
          phoneNumber: '+1234567890',
          password: 'password123',
          projectIds: [],
        }),
      });
    });

    expect(defaultProps.onSuccess).toHaveBeenCalled();
    expect(defaultProps.onClose).toHaveBeenCalled();
  });

  it('should submit form with project assignments', async () => {
    const user = userEvent.setup();
    
    // Mock successful user creation
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: { id: 'newuser1' },
        message: 'SPV user created successfully',
      }),
    } as Response);

    render(<SPVUserCreateForm {...defaultProps} />);

    // Fill in required fields
    await user.type(screen.getByLabelText(/full name/i), 'John Doe');
    await user.type(screen.getByLabelText(/email address/i), '<EMAIL>');
    
    // Select role
    const roleSelect = screen.getByRole('combobox', { name: /role/i });
    await user.click(roleSelect);
    await user.click(screen.getByRole('option', { name: /project manager/i }));

    await user.type(screen.getByLabelText(/^password/i), 'password123');
    await user.type(screen.getByLabelText(/confirm password/i), 'password123');

    // Enable project assignment
    const checkbox = screen.getByRole('checkbox', { name: /assign to projects now/i });
    await user.click(checkbox);

    await waitFor(() => {
      expect(screen.getByText('Test Project 1')).toBeInTheDocument();
    });

    // Select a project
    const projectCheckbox = screen.getByRole('checkbox', { name: /test project 1/i });
    await user.click(projectCheckbox);

    const submitButton = screen.getByRole('button', { name: /create user/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledWith('/api/spv/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          name: 'John Doe',
          role: 'PROJECT_MANAGER',
          jobTitle: '',
          phoneNumber: '',
          password: 'password123',
          projectIds: ['project1'],
        }),
      });
    });
  });

  it('should handle API errors', async () => {
    const user = userEvent.setup();
    
    // Mock API error
    mockFetch.mockResolvedValueOnce({
      ok: false,
      json: async () => ({
        error: 'User with this email already exists',
      }),
    } as Response);

    render(<SPVUserCreateForm {...defaultProps} />);

    // Fill in required fields
    await user.type(screen.getByLabelText(/full name/i), 'John Doe');
    await user.type(screen.getByLabelText(/email address/i), '<EMAIL>');
    
    // Select role
    const roleSelect = screen.getByRole('combobox', { name: /role/i });
    await user.click(roleSelect);
    await user.click(screen.getByRole('option', { name: /project manager/i }));

    await user.type(screen.getByLabelText(/^password/i), 'password123');
    await user.type(screen.getByLabelText(/confirm password/i), 'password123');

    const submitButton = screen.getByRole('button', { name: /create user/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledWith('/api/spv/users', expect.any(Object));
    });

    // Should not call success callbacks on error
    expect(defaultProps.onSuccess).not.toHaveBeenCalled();
    expect(defaultProps.onClose).not.toHaveBeenCalled();
  });

  it('should close form when cancel is clicked', async () => {
    const user = userEvent.setup();
    render(<SPVUserCreateForm {...defaultProps} />);

    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    await user.click(cancelButton);

    expect(defaultProps.onClose).toHaveBeenCalled();
  });

  it('should not render when isOpen is false', () => {
    render(<SPVUserCreateForm {...defaultProps} isOpen={false} />);

    expect(screen.queryByText(/create spv user/i)).not.toBeInTheDocument();
  });
});
