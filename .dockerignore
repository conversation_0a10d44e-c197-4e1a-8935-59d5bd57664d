# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
.pnpm-store/
.pnpm/

# Next.js build output
.next/
out/

# Production build
build/
dist/

# Large files that slow down context transfer
*.mp4
*.avi
*.mov
*.mkv
*.zip
*.tar.gz
*.rar

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Temporary folders
tmp/
temp/

# Database
*.db
*.sqlite

# Testing
test-results/
playwright-report/
playwright/.cache/

# Storybook build outputs
.out
.storybook-out

# Prisma
prisma/migrations/

# Local development
.env.example

# Backup files
*.backup
*.bak
*.tmp

# Documentation (not needed for build)
docs/
README.md
CHANGELOG.md
*.md

# Development tools
.husky/
.github/
.vscode/
.idea/

# Test files
**/*.test.ts
**/*.test.js
**/*.spec.ts
**/*.spec.js
__tests__/
tests/
